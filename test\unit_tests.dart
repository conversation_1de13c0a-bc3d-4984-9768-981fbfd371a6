/// اختبارات الوحدة (Unit Tests) لتطبيق أسامة ماركت
///
/// تتضمن اختبارات للوظائف الأساسية والمنطق التجاري
/// للتأكد من عمل الكود بشكل صحيح
library unit_tests;

import 'package:flutter_test/flutter_test.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/utils/formatters.dart';
import 'package:inventory_management_app/utils/validators.dart';
import 'package:inventory_management_app/config/app_colors.dart';

void main() {
  group('اختبارات النماذج (Models)', () {
    test('يجب أن ينشئ منتج بشكل صحيح', () {
      // إنشاء منتج جديد
      final Product product = Product(
        id: 1,
        name: 'منتج تجريبي',
        barcode: '123456789',
        category: 'فئة تجريبية',
        unit: 'قطعة',
        price: 10.0,
        warehouseQuantity: 100,
        storeQuantity: 50,
        wholesalePrice: 10.0,
        retailPrice: 15.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // التحقق من البيانات
      expect(product.id, equals(1));
      expect(product.name, equals('منتج تجريبي'));
      expect(product.barcode, equals('123456789'));
      expect(product.warehouseQuantity, equals(100));
      expect(product.storeQuantity, equals(50));
      expect(product.wholesalePrice, equals(10.0));
      expect(product.retailPrice, equals(15.0));
    });

    test('يجب أن يحول المنتج إلى Map بشكل صحيح', () {
      final Product product = Product(
        id: 1,
        name: 'منتج تجريبي',
        barcode: '123456789',
        category: 'فئة تجريبية',
        unit: 'قطعة',
        price: 10.0,
        warehouseQuantity: 100,
        storeQuantity: 50,
        wholesalePrice: 10.0,
        retailPrice: 15.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final Map<String, dynamic> productMap = product.toMap();

      expect(productMap['id'], equals(1));
      expect(productMap['name'], equals('منتج تجريبي'));
      expect(productMap['barcode'], equals('123456789'));
      expect(productMap['warehouse_quantity'], equals(100));
      expect(productMap['store_quantity'], equals(50));
    });

    test('يجب أن ينشئ عميل بشكل صحيح', () {
      final Customer customer = Customer(
        id: 1,
        name: 'عميل تجريبي',
        phone: '0123456789',
        address: 'عنوان تجريبي',
        balance: 100.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(customer.id, equals(1));
      expect(customer.name, equals('عميل تجريبي'));
      expect(customer.phone, equals('0123456789'));
      expect(customer.balance, equals(100.0));
    });

    test('يجب أن ينشئ بيع بشكل صحيح', () {
      final Sale sale = Sale(
        id: 1,
        customerId: 1,
        customerName: 'عميل تجريبي',
        totalAmount: 150.0,
        paidAmount: 100.0,
        remainingAmount: 50.0,
        saleType: 'retail',
        paymentMethod: 'cash',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(sale.id, equals(1));
      expect(sale.customerId, equals(1));
      expect(sale.totalAmount, equals(150.0));
      expect(sale.paidAmount, equals(100.0));
      expect(sale.remainingAmount, equals(50.0));
      expect(sale.saleType, equals('retail'));
    });
  });

  group('اختبارات المنسقات (Formatters)', () {
    test('يجب أن ينسق العملة بشكل صحيح', () {
      expect(Formatters.formatCurrency(100.0), equals('100.00 ر.س'));
      expect(Formatters.formatCurrency(1234.56), equals('1,234.56 ر.س'));
      expect(Formatters.formatCurrency(0), equals('0.00 ر.س'));
    });

    test('يجب أن ينسق التاريخ بشكل صحيح', () {
      final DateTime testDate = DateTime(2024, 12, 25, 14, 30);
      expect(Formatters.formatDate(testDate), equals('25/12/2024'));
    });

    test('يجب أن ينسق التاريخ والوقت بشكل صحيح', () {
      final DateTime testDateTime = DateTime(2024, 12, 25, 14, 30);
      expect(Formatters.formatDateTime(testDateTime),
          equals('25/12/2024 02:30 م'));
    });

    test('يجب أن ينسق الأرقام بشكل صحيح', () {
      expect(Formatters.formatNumber(1234), equals('1,234'));
      expect(Formatters.formatNumber(1234567), equals('1,234,567'));
      expect(Formatters.formatNumber(0), equals('0'));
    });
  });

  group('اختبارات المدققات (Validators)', () {
    test('يجب أن يدقق البريد الإلكتروني بشكل صحيح', () {
      expect(Validators.isValidEmail('<EMAIL>'), isTrue);
      expect(Validators.isValidEmail('<EMAIL>'), isTrue);
      expect(Validators.isValidEmail('invalid-email'), isFalse);
      expect(Validators.isValidEmail(''), isFalse);
      expect(Validators.isValidEmail('test@'), isFalse);
    });

    test('يجب أن يدقق رقم الهاتف بشكل صحيح', () {
      expect(Validators.isValidPhone('0501234567'), isTrue);
      expect(Validators.isValidPhone('966501234567'), isTrue);
      expect(Validators.isValidPhone('+966501234567'), isTrue);
      expect(Validators.isValidPhone('123'), isFalse);
      expect(Validators.isValidPhone(''), isFalse);
    });

    test('يجب أن يدقق الأسعار بشكل صحيح', () {
      expect(Validators.isValidPrice('10.50'), isTrue);
      expect(Validators.isValidPrice('100'), isTrue);
      expect(Validators.isValidPrice('0'), isTrue);
      expect(Validators.isValidPrice('-10'), isFalse);
      expect(Validators.isValidPrice('abc'), isFalse);
      expect(Validators.isValidPrice(''), isFalse);
    });

    test('يجب أن يدقق الكميات بشكل صحيح', () {
      expect(Validators.isValidQuantity('10'), isTrue);
      expect(Validators.isValidQuantity('100'), isTrue);
      expect(Validators.isValidQuantity('0'), isTrue);
      expect(Validators.isValidQuantity('-5'), isFalse);
      expect(Validators.isValidQuantity('abc'), isFalse);
      expect(Validators.isValidQuantity(''), isFalse);
    });
  });

  group('اختبارات الألوان (Colors)', () {
    test('يجب أن تكون الألوان الأساسية محددة بشكل صحيح', () {
      expect(AppColors.primary.value, isNotNull);
      expect(AppColors.accent.value, isNotNull);
      expect(AppColors.success.value, isNotNull);
      expect(AppColors.error.value, isNotNull);
      expect(AppColors.warning.value, isNotNull);
    });

    test('يجب أن تكون ألوان النصوص محددة بشكل صحيح', () {
      expect(AppColors.textPrimary.value, isNotNull);
      expect(AppColors.textSecondary.value, isNotNull);
      expect(AppColors.textOnPrimary.value, isNotNull);
    });

    test('يجب أن تكون الألوان الخاصة بالتطبيق محددة بشكل صحيح', () {
      expect(AppColors.sales.value, isNotNull);
      expect(AppColors.purchases.value, isNotNull);
      expect(AppColors.inventory.value, isNotNull);
      expect(AppColors.customers.value, isNotNull);
      expect(AppColors.suppliers.value, isNotNull);
      expect(AppColors.reports.value, isNotNull);
    });
  });

  group('اختبارات الحسابات التجارية', () {
    test('يجب أن يحسب إجمالي البيع بشكل صحيح', () {
      const double price = 10.0;
      const int quantity = 5;
      const double discount = 5.0;

      final double total = (price * quantity) - discount;

      expect(total, equals(45.0));
    });

    test('يجب أن يحسب الربح بشكل صحيح', () {
      const double sellingPrice = 15.0;
      const double costPrice = 10.0;
      const int quantity = 10;

      final double profit = (sellingPrice - costPrice) * quantity;

      expect(profit, equals(50.0));
    });

    test('يجب أن يحسب نسبة الربح بشكل صحيح', () {
      const double sellingPrice = 15.0;
      const double costPrice = 10.0;

      final double profitPercentage =
          ((sellingPrice - costPrice) / costPrice) * 100;

      expect(profitPercentage, equals(50.0));
    });

    test('يجب أن يحسب المبلغ المتبقي بشكل صحيح', () {
      const double totalAmount = 100.0;
      const double paidAmount = 60.0;

      final double remainingAmount = totalAmount - paidAmount;

      expect(remainingAmount, equals(40.0));
    });
  });
}
