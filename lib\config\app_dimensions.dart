/// App Dimensions - أبعاد التطبيق
/// يحتوي على جميع الأبعاد والمسافات المستخدمة في التطبيق
class AppDimensions {
  // ==================== Padding ====================

  /// Padding صغير جداً - 4.0
  static const double paddingXS = 4.0;

  /// Padding صغير - 8.0
  static const double paddingS = 8.0;

  /// Padding متوسط - 16.0
  static const double paddingM = 16.0;

  /// Padding كبير - 24.0
  static const double paddingL = 24.0;

  /// Padding كبير جداً - 32.0
  static const double paddingXL = 32.0;

  // ==================== Margin ====================

  /// Margin صغير - 8.0
  static const double marginS = 8.0;

  /// Margin متوسط - 16.0
  static const double marginM = 16.0;

  /// Margin كبير - 24.0
  static const double marginL = 24.0;

  /// Margin كبير جداً - 32.0
  static const double marginXL = 32.0;

  // ==================== Border Radius ====================

  /// Border radius صغير - 4.0
  static const double radiusS = 4.0;

  /// Border radius متوسط - 8.0
  static const double radiusM = 8.0;

  /// Border radius كبير - 12.0
  static const double radiusL = 12.0;

  /// Border radius كبير جداً - 16.0
  static const double radiusXL = 16.0;

  // ==================== Elevation ====================

  /// Elevation صغير - 2.0
  static const double elevationS = 2.0;

  /// Elevation متوسط - 4.0
  static const double elevationM = 4.0;

  /// Elevation كبير - 8.0
  static const double elevationL = 8.0;

  /// Elevation كبير جداً - 16.0
  static const double elevationXL = 16.0;

  // ==================== Button Dimensions ====================

  /// ارتفاع الأزرار الافتراضي - 48.0
  static const double buttonHeight = 48.0;

  /// ارتفاع الأزرار الصغيرة - 36.0
  static const double buttonHeightSmall = 36.0;

  /// ارتفاع الأزرار الكبيرة - 56.0
  static const double buttonHeightLarge = 56.0;

  /// عرض الأزرار الافتراضي - 120.0
  static const double buttonWidth = 120.0;

  // ==================== Icon Sizes ====================

  /// حجم الأيقونات الصغيرة - 16.0
  static const double iconSizeS = 16.0;

  /// حجم الأيقونات المتوسطة - 24.0
  static const double iconSizeM = 24.0;

  /// حجم الأيقونات الكبيرة - 32.0
  static const double iconSizeL = 32.0;

  /// حجم الأيقونات الكبيرة جداً - 48.0
  static const double iconSizeXL = 48.0;

  // Aliases for backward compatibility
  static const double iconS = iconSizeS;
  static const double iconM = iconSizeM;
  static const double iconL = iconSizeL;
  static const double iconXL = iconSizeXL;

  // ==================== App Bar ====================

  /// ارتفاع شريط التطبيق - 56.0
  static const double appBarHeight = 56.0;

  /// ارتفاع شريط التطبيق الكبير - 72.0
  static const double appBarHeightLarge = 72.0;

  // ==================== Bottom Navigation ====================

  /// ارتفاع شريط التنقل السفلي - 60.0
  static const double bottomNavHeight = 60.0;

  // ==================== Card Dimensions ====================

  /// ارتفاع البطاقات الصغيرة - 80.0
  static const double cardHeightSmall = 80.0;

  /// ارتفاع البطاقات المتوسطة - 120.0
  static const double cardHeightMedium = 120.0;

  /// ارتفاع البطاقات الكبيرة - 160.0
  static const double cardHeightLarge = 160.0;

  /// ارتفاع بطاقات لوحة التحكم - 140.0
  static const double dashboardCardHeight = 140.0;

  // ==================== List Item Dimensions ====================

  /// ارتفاع عناصر القائمة - 56.0
  static const double listItemHeight = 56.0;

  /// ارتفاع عناصر القائمة الكبيرة - 72.0
  static const double listItemHeightLarge = 72.0;

  // ==================== Input Field Dimensions ====================

  /// ارتفاع حقول الإدخال - 48.0
  static const double inputHeight = 48.0;

  /// ارتفاع حقول الإدخال الكبيرة - 56.0
  static const double inputHeightLarge = 56.0;

  // ==================== Divider ====================

  /// سمك الفواصل - 1.0
  static const double dividerThickness = 1.0;

  /// سمك الفواصل السميكة - 2.0
  static const double dividerThicknessBold = 2.0;

  // ==================== Avatar Sizes ====================

  /// حجم الصور الشخصية الصغيرة - 32.0
  static const double avatarSizeS = 32.0;

  /// حجم الصور الشخصية المتوسطة - 48.0
  static const double avatarSizeM = 48.0;

  /// حجم الصور الشخصية الكبيرة - 64.0
  static const double avatarSizeL = 64.0;

  /// حجم الصور الشخصية الكبيرة جداً - 96.0
  static const double avatarSizeXL = 96.0;

  // ==================== Screen Breakpoints ====================

  /// نقطة انقطاع الشاشات الصغيرة - 600.0
  static const double breakpointSmall = 600.0;

  /// نقطة انقطاع الشاشات المتوسطة - 900.0
  static const double breakpointMedium = 900.0;

  /// نقطة انقطاع الشاشات الكبيرة - 1200.0
  static const double breakpointLarge = 1200.0;

  // ==================== Animation Durations ====================

  /// مدة الرسوم المتحركة السريعة - 200ms
  static const Duration animationFast = Duration(milliseconds: 200);

  /// مدة الرسوم المتحركة المتوسطة - 300ms
  static const Duration animationMedium = Duration(milliseconds: 300);

  /// مدة الرسوم المتحركة البطيئة - 500ms
  static const Duration animationSlow = Duration(milliseconds: 500);

  // ==================== Helper Methods ====================

  /// الحصول على padding حسب الحجم
  static double getPadding(String size) {
    switch (size.toLowerCase()) {
      case 's':
      case 'small':
        return paddingS;
      case 'm':
      case 'medium':
        return paddingM;
      case 'l':
      case 'large':
        return paddingL;
      case 'xl':
      case 'xlarge':
        return paddingXL;
      default:
        return paddingM;
    }
  }

  /// الحصول على margin حسب الحجم
  static double getMargin(String size) {
    switch (size.toLowerCase()) {
      case 's':
      case 'small':
        return marginS;
      case 'm':
      case 'medium':
        return marginM;
      case 'l':
      case 'large':
        return marginL;
      case 'xl':
      case 'xlarge':
        return marginXL;
      default:
        return marginM;
    }
  }

  /// الحصول على border radius حسب الحجم
  static double getRadius(String size) {
    switch (size.toLowerCase()) {
      case 's':
      case 'small':
        return radiusS;
      case 'm':
      case 'medium':
        return radiusM;
      case 'l':
      case 'large':
        return radiusL;
      case 'xl':
      case 'xlarge':
        return radiusXL;
      default:
        return radiusM;
    }
  }

  /// الحصول على elevation حسب الحجم
  static double getElevation(String size) {
    switch (size.toLowerCase()) {
      case 's':
      case 'small':
        return elevationS;
      case 'm':
      case 'medium':
        return elevationM;
      case 'l':
      case 'large':
        return elevationL;
      case 'xl':
      case 'xlarge':
        return elevationXL;
      default:
        return elevationM;
    }
  }
}
