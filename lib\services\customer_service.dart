import 'package:sqflite/sqflite.dart';
import '../models/customer.dart';
import 'database_service.dart';

/// Service class for handling Customer CRUD operations
class CustomerService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all customers from the database
  Future<List<Customer>> getAllCustomers() async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('customers');

    return List.generate(maps.length, (int i) {
      return Customer.fromMap(maps[i]);
    });
  }

  /// Get a customer by its ID
  Future<Customer?> getCustomerById(int id) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );

    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new customer into the database
  Future<int> insertCustomer(Customer customer) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'customers',
      customer.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing customer in the database
  Future<int> updateCustomer(Customer customer) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'customers',
      customer.toMap(),
      where: 'id = ?',
      whereArgs: <Object?>[customer.id],
    );
  }

  /// Delete a customer from the database
  Future<int> deleteCustomer(int id) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'customers',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );
  }

  /// Search customers by name
  Future<List<Customer>> searchCustomersByName(String name) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'name LIKE ?',
      whereArgs: <Object?>['%$name%'],
    );

    return List.generate(maps.length, (int i) {
      return Customer.fromMap(maps[i]);
    });
  }

  /// Search customers by email
  Future<List<Customer>> searchCustomersByEmail(String email) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'email LIKE ?',
      whereArgs: <Object?>['%$email%'],
    );

    return List.generate(maps.length, (int i) {
      return Customer.fromMap(maps[i]);
    });
  }

  /// Search customers by phone
  Future<List<Customer>> searchCustomersByPhone(String phone) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'phone LIKE ?',
      whereArgs: <Object?>['%$phone%'],
    );

    return List.generate(maps.length, (int i) {
      return Customer.fromMap(maps[i]);
    });
  }

  /// Get total number of customers
  Future<int> getCustomerCount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result =
        await db.rawQuery('SELECT COUNT(*) as count FROM customers');
    return result.first['count'] as int;
  }

  /// Check if customer email already exists
  Future<bool> customerEmailExists(String email, {int? excludeId}) async {
    final Database db = await _databaseService.database;
    String whereClause = 'email = ?';
    List<dynamic> whereArgs = <dynamic>[email];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isNotEmpty;
  }

  /// Check if customer name already exists
  Future<bool> customerNameExists(String name, {int? excludeId}) async {
    final Database db = await _databaseService.database;
    String whereClause = 'name = ?';
    List<dynamic> whereArgs = <dynamic>[name];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isNotEmpty;
  }

  /// Check if customer phone already exists
  Future<bool> customerPhoneExists(String phone, {int? excludeId}) async {
    final Database db = await _databaseService.database;
    String whereClause = 'phone = ?';
    List<dynamic> whereArgs = <dynamic>[phone];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isNotEmpty;
  }

  /// Add a new customer (alias for insertCustomer)
  Future<int> addCustomer(Customer customer) async {
    return await insertCustomer(customer);
  }

  /// Get customer by phone number
  Future<Customer?> getCustomerByPhone(String phone) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'phone = ?',
      whereArgs: <Object?>[phone],
    );

    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  /// Update customer balance
  Future<int> updateCustomerBalance(int customerId, double newBalance) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'customers',
      <String, Object?>{
        'balance': newBalance,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: <Object?>[customerId],
    );
  }
}
