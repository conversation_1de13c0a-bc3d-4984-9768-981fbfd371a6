import 'package:sqflite/sqflite.dart';
import '../models/supplier.dart';
import 'database_service.dart';

/// Service class for handling Supplier CRUD operations
class SupplierService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all suppliers from the database
  Future<List<Supplier>> getAllSuppliers() async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('suppliers');

    return List.generate(maps.length, (int i) {
      return Supplier.fromMap(maps[i]);
    });
  }

  /// Get a supplier by its ID
  Future<Supplier?> getSupplierById(int id) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'suppliers',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );

    if (maps.isNotEmpty) {
      return Supplier.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new supplier into the database
  Future<int> insertSupplier(Supplier supplier) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'suppliers',
      supplier.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing supplier in the database
  Future<int> updateSupplier(Supplier supplier) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'suppliers',
      supplier.toMap(),
      where: 'id = ?',
      whereArgs: <Object?>[supplier.id],
    );
  }

  /// Delete a supplier from the database
  Future<int> deleteSupplier(int id) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'suppliers',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );
  }

  /// Search suppliers by name
  Future<List<Supplier>> searchSuppliersByName(String name) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'suppliers',
      where: 'name LIKE ?',
      whereArgs: <Object?>['%$name%'],
    );

    return List.generate(maps.length, (int i) {
      return Supplier.fromMap(maps[i]);
    });
  }

  /// Search suppliers by email
  Future<List<Supplier>> searchSuppliersByEmail(String email) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'suppliers',
      where: 'email LIKE ?',
      whereArgs: <Object?>['%$email%'],
    );

    return List.generate(maps.length, (int i) {
      return Supplier.fromMap(maps[i]);
    });
  }

  /// Get total number of suppliers
  Future<int> getSupplierCount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result =
        await db.rawQuery('SELECT COUNT(*) as count FROM suppliers');
    return result.first['count'] as int;
  }

  /// Check if supplier email already exists
  Future<bool> supplierEmailExists(String email, {int? excludeId}) async {
    final Database db = await _databaseService.database;
    String whereClause = 'email = ?';
    List<dynamic> whereArgs = <dynamic>[email];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'suppliers',
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isNotEmpty;
  }

  /// Check if supplier name already exists
  Future<bool> supplierNameExists(String name, {int? excludeId}) async {
    final Database db = await _databaseService.database;
    String whereClause = 'name = ?';
    List<dynamic> whereArgs = <dynamic>[name];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'suppliers',
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isNotEmpty;
  }

  /// Check if supplier phone already exists
  Future<bool> supplierPhoneExists(String phone, {int? excludeId}) async {
    final Database db = await _databaseService.database;
    String whereClause = 'phone = ?';
    List<dynamic> whereArgs = <dynamic>[phone];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'suppliers',
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isNotEmpty;
  }
}
