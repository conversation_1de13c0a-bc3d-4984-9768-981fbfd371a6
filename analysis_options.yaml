# analysis_options.yaml

include: package:flutter_lints/flutter.yaml

analyzer:
  errors:
    unused_import: warning
    dead_code: warning
    missing_return: error
    unnecessary_getters_setters: warning
    unnecessary_this: warning

linter:
  rules:
    - prefer_single_quotes
    - always_specify_types
    - avoid_print
    - public_member_api_docs
    - lines_longer_than_80_chars
    - prefer_final_fields
    - type_annotate_public_apis
    - unnecessary_new
    - prefer_const_constructors
    - avoid_renaming_method_parameters