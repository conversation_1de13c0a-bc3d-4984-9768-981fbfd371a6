# دليل النظام الموحد للألوان ومعالجة الأخطاء

## 🎨 **استخدام الألوان الموحدة**

### **✅ الطريقة الصحيحة:**
```dart
import '../../config/app_colors.dart';

// استخدام الألوان الموحدة
Container(
  color: AppColors.primary,        // بدلاً من Colors.blue
  child: Text(
    'نص',
    style: TextStyle(color: AppColors.textOnPrimary), // بدلاً من Colors.white
  ),
)
```

### **❌ تجنب هذا:**
```dart
// لا تستخدم الألوان المباشرة
Container(
  color: Colors.blue,              // خطأ!
  child: Text(
    'نص',
    style: TextStyle(color: Colors.white), // خطأ!
  ),
)
```

### **الألوان المتاحة:**
```dart
// الألوان الأساسية
AppColors.primary          // الأزرق الأساسي
AppColors.accent           // الكهرماني للتمييز
AppColors.secondary        // التيل للنجاح
AppColors.background       // الخلفية البيضاء
AppColors.surface          // سطح البطاقات

// ألوان النصوص
AppColors.textPrimary      // النص الأساسي
AppColors.textSecondary    // النص الثانوي
AppColors.textOnPrimary    // النص على الألوان الأساسية

// ألوان الحالة
AppColors.success          // الأخضر للنجاح
AppColors.error            // الأحمر للأخطاء
AppColors.warning          // البرتقالي للتحذيرات
AppColors.info             // الأزرق للمعلومات

// ألوان خاصة بالتطبيق
AppColors.sales            // لون المبيعات
AppColors.purchases        // لون المشتريات
AppColors.inventory        // لون المخزون
AppColors.customers        // لون العملاء
AppColors.suppliers        // لون الموردين
AppColors.reports          // لون التقارير
```

## 📢 **استخدام معالجة الأخطاء الموحدة**

### **✅ الطريقة الصحيحة:**
```dart
import '../../utils/snackbar_helper.dart';

// عرض رسائل النجاح
SnackBarHelper.showSuccess(context, 'تم الحفظ بنجاح');

// عرض رسائل الخطأ
SnackBarHelper.showError(context, 'حدث خطأ في الحفظ');

// عرض رسائل التحذير
SnackBarHelper.showWarning(context, 'تحذير: البيانات غير مكتملة');

// عرض رسائل المعلومات
SnackBarHelper.showInfo(context, 'معلومة: تم التحديث');

// عرض رسائل التحميل
SnackBarHelper.showLoading(context, 'جاري التحميل...');

// حوارات مفصلة
DialogHelper.showError(
  context,
  title: 'خطأ',
  message: 'فشل في الاتصال بالخادم',
);

DialogHelper.showSuccess(
  context,
  title: 'نجح',
  message: 'تم حفظ البيانات بنجاح',
);
```

### **❌ تجنب هذا:**
```dart
// لا تستخدم ScaffoldMessenger مباشرة
ScaffoldMessenger.of(context).showSnackBar(
  SnackBar(
    content: Text('رسالة'),
    backgroundColor: Colors.green,  // خطأ!
  ),
);
```

## 🎯 **أمثلة عملية**

### **مثال 1: شاشة إضافة منتج**
```dart
import '../../config/app_colors.dart';
import '../../utils/snackbar_helper.dart';

class AddProductScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        title: Text('إضافة منتج'),
      ),
      body: Container(
        color: AppColors.surface,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.success,
          ),
          onPressed: () {
            // عند النجاح
            SnackBarHelper.showSuccess(context, 'تم إضافة المنتج بنجاح');
            
            // عند الخطأ
            SnackBarHelper.showError(context, 'فشل في إضافة المنتج');
          },
          child: Text('حفظ'),
        ),
      ),
    );
  }
}
```

### **مثال 2: بطاقة إحصائية**
```dart
Widget buildStatCard(String title, String value, String type) {
  Color cardColor;
  
  switch (type) {
    case 'sales':
      cardColor = AppColors.sales;
      break;
    case 'purchases':
      cardColor = AppColors.purchases;
      break;
    case 'inventory':
      cardColor = AppColors.inventory;
      break;
    default:
      cardColor = AppColors.primary;
  }
  
  return Card(
    color: AppColors.surface,
    child: Container(
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: cardColor, width: 3)),
      ),
      child: Column(
        children: [
          Text(title, style: TextStyle(color: AppColors.textSecondary)),
          Text(value, style: TextStyle(color: cardColor)),
        ],
      ),
    ),
  );
}
```

## 📝 **قواعد مهمة**

1. **دائماً استخدم AppColors** بدلاً من Colors المباشرة
2. **دائماً استخدم SnackBarHelper** بدلاً من ScaffoldMessenger
3. **أضف imports مطلوبة** في بداية كل ملف
4. **اختبر الرسائل** للتأكد من ظهورها بشكل صحيح
5. **استخدم الألوان المناسبة** لكل نوع من البيانات

## 🔧 **إضافة ألوان جديدة**

إذا احتجت لون جديد، أضفه في `app_colors.dart`:

```dart
// في lib/config/app_colors.dart
class AppColors {
  // ... الألوان الموجودة
  
  // لون جديد
  static const Color newFeature = Color(0xFF123456);
  static const Color newFeatureLight = Color(0xFF789ABC);
}
```

## 🚀 **نصائح للأداء**

1. استخدم `const` مع الألوان عند الإمكان
2. تجنب إنشاء ألوان جديدة في كل build
3. استخدم `AppStyles` للنصوص المتكررة
4. اختبر الألوان في الوضع الداكن والفاتح

---

**تذكر:** النظام الموحد يجعل التطبيق أكثر احترافية وسهولة في الصيانة! 🎨✨
