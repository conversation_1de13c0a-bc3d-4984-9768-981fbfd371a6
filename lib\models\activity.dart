/// Model class representing a system activity log
class Activity {
  /// Unique identifier for the activity
  int? id;

  /// Type of activity (e.g., "sale", "purchase", "login")
  String? type;

  /// Description of the activity
  String? description;

  /// Date when the activity occurred
  String? date;

  /// Constructor for creating an Activity instance
  Activity({
    this.id,
    this.type,
    this.description,
    this.date,
  });

  /// Converts the Activity instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'type': type,
      'description': description,
      'date': date,
    };
  }

  /// Creates an Activity instance from a Map (typically from database)
  factory Activity.fromMap(Map<String, dynamic> map) {
    return Activity(
      id: map['id'] as int?,
      type: map['type'] as String?,
      description: map['description'] as String?,
      date: map['date'] as String?,
    );
  }

  @override
  String toString() {
    return 'Activity{id: $id, type: $type, '
        'description: $description, date: $date}';
  }
}
