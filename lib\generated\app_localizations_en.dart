// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Osama Market';

  @override
  String get appSubtitle => 'Smart Management for Your Store';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get sales => 'Sales';

  @override
  String get purchases => 'Purchases';

  @override
  String get products => 'Products';

  @override
  String get customers => 'Customers';

  @override
  String get suppliers => 'Suppliers';

  @override
  String get inventory => 'Inventory';

  @override
  String get reports => 'Reports';

  @override
  String get settings => 'Settings';

  @override
  String get backup => 'Backup';

  @override
  String get add => 'Add';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get ok => 'OK';

  @override
  String get close => 'Close';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get sort => 'Sort';

  @override
  String get refresh => 'Refresh';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Info';

  @override
  String get name => 'Name';

  @override
  String get description => 'Description';

  @override
  String get price => 'Price';

  @override
  String get quantity => 'Quantity';

  @override
  String get total => 'Total';

  @override
  String get date => 'Date';

  @override
  String get time => 'Time';

  @override
  String get phone => 'Phone';

  @override
  String get address => 'Address';

  @override
  String get email => 'Email';

  @override
  String get notes => 'Notes';

  @override
  String get currency => 'SAR';

  @override
  String get welcomeMessage => 'Welcome to Osama Market';

  @override
  String get noDataFound => 'No data found';

  @override
  String get confirmDelete => 'Are you sure you want to delete?';

  @override
  String get operationSuccessful => 'Operation completed successfully';

  @override
  String get operationFailed => 'Operation failed';

  @override
  String get permissionRequired => 'Permission Required';

  @override
  String get permissionDenied => 'Permission Denied';

  @override
  String get storagePermission => 'Storage Permission';

  @override
  String get cameraPermission => 'Camera Permission';

  @override
  String get locationPermission => 'Location Permission';

  @override
  String get contactsPermission => 'Contacts Permission';
}
