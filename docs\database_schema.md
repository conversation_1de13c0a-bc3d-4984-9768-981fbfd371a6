# Database Schema Documentation

## Overview
This document describes the database schema for the Inventory Management Application. The application uses SQLite as the database engine with the following tables and relationships.

## Tables

### 1. Products Table
Stores information about products in the inventory.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| name | TEXT | NOT NULL | Product name |
| categoryId | INTEGER | FOREIGN KEY | Reference to categories table |
| unitId | INTEGER | FOREIGN KEY | Reference to units table |
| price | REAL | | Product price |
| quantity | REAL | | Current stock quantity |
| supplierId | INTEGER | FOREIGN KEY | Reference to suppliers table |
| description | TEXT | | Product description |

### 2. Categories Table
Stores product categories.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| name | TEXT | NOT NULL | Category name |
| description | TEXT | | Category description |

### 3. Units Table
Stores measurement units for products.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| name | TEXT | NOT NULL | Unit name (e.g., "Kilogram", "Piece") |
| symbol | TEXT | | Unit symbol (e.g., "kg", "pcs") |

### 4. Suppliers Table
Stores supplier information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| name | TEXT | NOT NULL | Supplier name |
| email | TEXT | | Supplier email |
| phone | TEXT | | Supplier phone |
| address | TEXT | | Supplier address |

### 5. Customers Table
Stores customer information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| name | TEXT | NOT NULL | Customer name |
| email | TEXT | | Customer email |
| phone | TEXT | | Customer phone |
| address | TEXT | | Customer address |

### 6. Sales Table
Stores sales transactions.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| customerId | INTEGER | FOREIGN KEY | Reference to customers table |
| date | TEXT | | Sale date |
| total | REAL | | Total sale amount |
| notes | TEXT | | Additional notes |

### 7. Sale Items Table
Stores individual items in each sale.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| saleId | INTEGER | FOREIGN KEY | Reference to sales table |
| productId | INTEGER | FOREIGN KEY | Reference to products table |
| quantity | REAL | | Quantity sold |
| price | REAL | | Price per unit |

### 8. Purchases Table
Stores purchase transactions from suppliers.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| supplierId | INTEGER | FOREIGN KEY | Reference to suppliers table |
| date | TEXT | | Purchase date |
| total | REAL | | Total purchase amount |
| notes | TEXT | | Additional notes |

### 9. Purchase Items Table
Stores individual items in each purchase.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| purchaseId | INTEGER | FOREIGN KEY | Reference to purchases table |
| productId | INTEGER | FOREIGN KEY | Reference to products table |
| quantity | REAL | | Quantity purchased |
| price | REAL | | Price per unit |

### 10. Orders Table
Stores customer orders.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| customerId | INTEGER | FOREIGN KEY | Reference to customers table |
| date | TEXT | | Order date |
| status | TEXT | | Order status |
| total | REAL | | Total order amount |

### 11. Order Items Table
Stores individual items in each order.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| orderId | INTEGER | FOREIGN KEY | Reference to orders table |
| productId | INTEGER | FOREIGN KEY | Reference to products table |
| quantity | REAL | | Quantity ordered |
| price | REAL | | Price per unit |

### 12. Expenses Table
Stores business expenses.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | Unique identifier |
| categoryId | INTEGER | FOREIGN KEY | Reference to categories table |
| amount | REAL | | Expense amount |
| date | TEXT | | Expense date |
| notes | TEXT | | Additional notes |

## Relationships

### Foreign Key Relationships
- **products.categoryId** → **categories.id**
- **products.unitId** → **units.id**
- **products.supplierId** → **suppliers.id**
- **sales.customerId** → **customers.id**
- **sale_items.saleId** → **sales.id**
- **sale_items.productId** → **products.id**
- **purchases.supplierId** → **suppliers.id**
- **purchase_items.purchaseId** → **purchases.id**
- **purchase_items.productId** → **products.id**
- **orders.customerId** → **customers.id**
- **order_items.orderId** → **orders.id**
- **order_items.productId** → **products.id**
- **expenses.categoryId** → **categories.id**

## Entity Relationship Diagram

```
Categories ──┐
             ├── Products ──┐
Units ───────┘              ├── Sale Items ── Sales ── Customers
Suppliers ───────────────────┤
                             ├── Purchase Items ── Purchases ── Suppliers
                             └── Order Items ── Orders ── Customers
```

## Indexes
Consider adding indexes on frequently queried columns:
- products.name
- products.categoryId
- customers.email
- suppliers.email
- sales.date
- purchases.date

## Notes
- All date fields are stored as TEXT in ISO 8601 format
- Quantities and prices use REAL type to support decimal values
- Foreign key constraints ensure referential integrity
- All tables use INTEGER PRIMARY KEY AUTOINCREMENT for unique identifiers
