import 'package:sqflite/sqflite.dart';
import 'package:inventory_management_app/services/database_service.dart';

/// Service for generating various reports and analytics
class ReportsService {
  static final ReportsService _instance = ReportsService._internal();
  factory ReportsService() => _instance;
  ReportsService._internal();

  final DatabaseService _databaseService = DatabaseService();

  /// Get dashboard summary data
  Future<Map<String, dynamic>> getDashboardSummary() async {
    try {
      final Database db = await _databaseService.database;

      // Get total counts
      final List<Map<String, dynamic>> productCount =
          await db.rawQuery('SELECT COUNT(*) as count FROM products');
      final List<Map<String, dynamic>> customerCount =
          await db.rawQuery('SELECT COUNT(*) as count FROM customers');
      final List<Map<String, dynamic>> supplierCount =
          await db.rawQuery('SELECT COUNT(*) as count FROM suppliers');
      final List<Map<String, dynamic>> orderCount =
          await db.rawQuery('SELECT COUNT(*) as count FROM orders');

      // Get financial totals
      final List<Map<String, dynamic>> totalSales = await db.rawQuery(
          'SELECT COALESCE(SUM(total), 0) as total FROM sales WHERE date >= date("now", "-30 days")');
      final List<Map<String, dynamic>> totalPurchases = await db.rawQuery(
          'SELECT COALESCE(SUM(total), 0) as total FROM purchases WHERE date >= date("now", "-30 days")');
      final List<Map<String, dynamic>> totalExpenses = await db.rawQuery(
          'SELECT COALESCE(SUM(amount), 0) as total FROM expenses WHERE date >= date("now", "-30 days")');

      // Get low stock products
      final List<Map<String, dynamic>> lowStockProducts = await db.rawQuery(
          'SELECT COUNT(*) as count FROM products WHERE quantity <= 10');

      return <String, dynamic>{
        'totalProducts': productCount.first['count'] ?? 0,
        'totalCustomers': customerCount.first['count'] ?? 0,
        'totalSuppliers': supplierCount.first['count'] ?? 0,
        'totalOrders': orderCount.first['count'] ?? 0,
        'totalSales': (totalSales.first['total'] ?? 0.0) as double,
        'totalPurchases': (totalPurchases.first['total'] ?? 0.0) as double,
        'totalExpenses': (totalExpenses.first['total'] ?? 0.0) as double,
        'lowStockProducts': lowStockProducts.first['count'] ?? 0,
        'profit': ((totalSales.first['total'] ?? 0.0) as double) -
            ((totalPurchases.first['total'] ?? 0.0) as double) -
            ((totalExpenses.first['total'] ?? 0.0) as double),
      };
    } catch (e) {
      throw Exception('Failed to get dashboard summary: $e');
    }
  }

  /// Get sales report for a specific period
  Future<Map<String, dynamic>> getSalesReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final Database db = await _databaseService.database;

      final String startDateStr = startDate.toIso8601String().split('T')[0];
      final String endDateStr = endDate.toIso8601String().split('T')[0];

      // Get total sales
      final List<Map<String, dynamic>> totalSales = await db.rawQuery(
          'SELECT COALESCE(SUM(total), 0) as total, COUNT(*) as count FROM sales WHERE date BETWEEN ? AND ?',
          <String>[startDateStr, endDateStr]);

      // Get daily sales
      final List<Map<String, dynamic>> dailySales = await db.rawQuery(
          'SELECT date, COALESCE(SUM(total), 0) as total, COUNT(*) as count FROM sales WHERE date BETWEEN ? AND ? GROUP BY date ORDER BY date',
          <String>[startDateStr, endDateStr]);

      // Get top selling products
      final List<Map<String, dynamic>> topProducts = await db.rawQuery(
          '''SELECT p.name, COALESCE(SUM(s.quantity), 0) as quantity_sold, COALESCE(SUM(s.total), 0) as total_sales
           FROM products p
           LEFT JOIN sales s ON p.id = s.productId AND s.date BETWEEN ? AND ?
           GROUP BY p.id, p.name
           ORDER BY quantity_sold DESC
           LIMIT 10''', <String>[startDateStr, endDateStr]);

      // Get sales by customer
      final List<Map<String, dynamic>> salesByCustomer = await db.rawQuery(
          '''SELECT c.name, COALESCE(SUM(s.total), 0) as total_sales, COUNT(s.id) as order_count
           FROM customers c
           LEFT JOIN sales s ON c.id = s.customerId AND s.date BETWEEN ? AND ?
           GROUP BY c.id, c.name
           HAVING total_sales > 0
           ORDER BY total_sales DESC
           LIMIT 10''', <String>[startDateStr, endDateStr]);

      return <String, dynamic>{
        'period': <String, String>{
          'start': startDateStr,
          'end': endDateStr,
        },
        'summary': <String, dynamic>{
          'totalSales': (totalSales.first['total'] ?? 0.0) as double,
          'totalOrders': totalSales.first['count'] ?? 0,
          'averageOrderValue': totalSales.first['count'] > 0
              ? ((totalSales.first['total'] ?? 0.0) as double) /
                  (totalSales.first['count'] as int)
              : 0.0,
        },
        'dailySales': dailySales,
        'topProducts': topProducts,
        'salesByCustomer': salesByCustomer,
      };
    } catch (e) {
      throw Exception('Failed to get sales report: $e');
    }
  }

  /// Get inventory report
  Future<Map<String, dynamic>> getInventoryReport() async {
    try {
      final Database db = await _databaseService.database;

      // Get all products with stock info
      final List<Map<String, dynamic>> products = await db
          .rawQuery('''SELECT p.*, c.name as category_name, u.name as unit_name,
           CASE 
             WHEN p.quantity <= 10 THEN 'Low'
             WHEN p.quantity <= 50 THEN 'Medium'
             ELSE 'High'
           END as stock_level
           FROM products p
           LEFT JOIN categories c ON p.categoryId = c.id
           LEFT JOIN units u ON p.unitId = u.id
           ORDER BY p.quantity ASC''');

      // Get stock value
      final List<Map<String, dynamic>> stockValue = await db.rawQuery(
          'SELECT COALESCE(SUM(quantity * price), 0) as total_value FROM products WHERE price IS NOT NULL');

      // Get low stock products
      final List<Map<String, dynamic>> lowStockProducts = products
          .where((Map<String, dynamic> product) =>
              (product['quantity'] ?? 0) <= 10)
          .toList();

      // Get out of stock products
      final List<Map<String, dynamic>> outOfStockProducts = products
          .where(
              (Map<String, dynamic> product) => (product['quantity'] ?? 0) <= 0)
          .toList();

      // Get stock by category
      final List<Map<String, dynamic>> stockByCategory = await db
          .rawQuery('''SELECT c.name as category, COUNT(p.id) as product_count, 
           COALESCE(SUM(p.quantity), 0) as total_quantity,
           COALESCE(SUM(p.quantity * p.price), 0) as total_value
           FROM categories c
           LEFT JOIN products p ON c.id = p.categoryId
           GROUP BY c.id, c.name
           ORDER BY total_value DESC''');

      return <String, dynamic>{
        'summary': <String, dynamic>{
          'totalProducts': products.length,
          'totalStockValue': (stockValue.first['total_value'] ?? 0.0) as double,
          'lowStockCount': lowStockProducts.length,
          'outOfStockCount': outOfStockProducts.length,
        },
        'products': products,
        'lowStockProducts': lowStockProducts,
        'outOfStockProducts': outOfStockProducts,
        'stockByCategory': stockByCategory,
      };
    } catch (e) {
      throw Exception('Failed to get inventory report: $e');
    }
  }

  /// Get financial report for a specific period
  Future<Map<String, dynamic>> getFinancialReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final Database db = await _databaseService.database;

      final String startDateStr = startDate.toIso8601String().split('T')[0];
      final String endDateStr = endDate.toIso8601String().split('T')[0];

      // Get sales data
      final List<Map<String, dynamic>> salesData = await db.rawQuery(
          'SELECT COALESCE(SUM(total), 0) as total FROM sales WHERE date BETWEEN ? AND ?',
          <String>[startDateStr, endDateStr]);

      // Get purchases data
      final List<Map<String, dynamic>> purchasesData = await db.rawQuery(
          'SELECT COALESCE(SUM(total), 0) as total FROM purchases WHERE date BETWEEN ? AND ?',
          <String>[startDateStr, endDateStr]);

      // Get expenses data
      final List<Map<String, dynamic>> expensesData = await db.rawQuery(
          'SELECT COALESCE(SUM(amount), 0) as total FROM expenses WHERE date BETWEEN ? AND ?',
          <String>[startDateStr, endDateStr]);

      // Get monthly breakdown
      final List<Map<String, dynamic>> monthlyBreakdown = await db.rawQuery(
          '''SELECT 
           strftime('%Y-%m', date) as month,
           COALESCE(SUM(CASE WHEN 'sales' = 'sales' THEN total ELSE 0 END), 0) as sales,
           0 as purchases,
           0 as expenses
           FROM sales 
           WHERE date BETWEEN ? AND ?
           GROUP BY strftime('%Y-%m', date)
           
           UNION ALL
           
           SELECT 
           strftime('%Y-%m', date) as month,
           0 as sales,
           COALESCE(SUM(total), 0) as purchases,
           0 as expenses
           FROM purchases 
           WHERE date BETWEEN ? AND ?
           GROUP BY strftime('%Y-%m', date)
           
           UNION ALL
           
           SELECT 
           strftime('%Y-%m', date) as month,
           0 as sales,
           0 as purchases,
           COALESCE(SUM(amount), 0) as expenses
           FROM expenses 
           WHERE date BETWEEN ? AND ?
           GROUP BY strftime('%Y-%m', date)
           
           ORDER BY month''',
          <String>[
            startDateStr,
            endDateStr,
            startDateStr,
            endDateStr,
            startDateStr,
            endDateStr
          ]);

      final double totalSales = (salesData.first['total'] ?? 0.0) as double;
      final double totalPurchases =
          (purchasesData.first['total'] ?? 0.0) as double;
      final double totalExpenses =
          (expensesData.first['total'] ?? 0.0) as double;
      final double grossProfit = totalSales - totalPurchases;
      final double netProfit = grossProfit - totalExpenses;

      return <String, dynamic>{
        'period': <String, String>{
          'start': startDateStr,
          'end': endDateStr,
        },
        'summary': <String, dynamic>{
          'totalSales': totalSales,
          'totalPurchases': totalPurchases,
          'totalExpenses': totalExpenses,
          'grossProfit': grossProfit,
          'netProfit': netProfit,
          'profitMargin': totalSales > 0 ? (netProfit / totalSales) * 100 : 0.0,
        },
        'monthlyBreakdown': monthlyBreakdown,
      };
    } catch (e) {
      throw Exception('Failed to get financial report: $e');
    }
  }

  /// Get customer statement
  Future<Map<String, dynamic>> getCustomerStatement({
    required int customerId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final Database db = await _databaseService.database;

      final String startDateStr = startDate.toIso8601String().split('T')[0];
      final String endDateStr = endDate.toIso8601String().split('T')[0];

      // Get customer info
      final List<Map<String, dynamic>> customerInfo = await db
          .rawQuery('SELECT * FROM customers WHERE id = ?', <int>[customerId]);

      if (customerInfo.isEmpty) {
        throw Exception('Customer not found');
      }

      // Get sales for this customer
      final List<Map<String, dynamic>> sales = await db.rawQuery(
          '''SELECT s.*, p.name as product_name
           FROM sales s
           LEFT JOIN products p ON s.productId = p.id
           WHERE s.customerId = ? AND s.date BETWEEN ? AND ?
           ORDER BY s.date DESC''',
          <Object?>[customerId, startDateStr, endDateStr]);

      // Calculate totals
      final double totalSales = sales.fold(
          0.0,
          (double sum, Map<String, dynamic> sale) =>
              sum + ((sale['total'] ?? 0.0) as double));

      return <String, dynamic>{
        'customer': customerInfo.first,
        'period': <String, String>{
          'start': startDateStr,
          'end': endDateStr,
        },
        'summary': <String, dynamic>{
          'totalSales': totalSales,
          'totalOrders': sales.length,
        },
        'transactions': sales,
      };
    } catch (e) {
      throw Exception('Failed to get customer statement: $e');
    }
  }

  /// Get supplier statement
  Future<Map<String, dynamic>> getSupplierStatement({
    required int supplierId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final Database db = await _databaseService.database;

      final String startDateStr = startDate.toIso8601String().split('T')[0];
      final String endDateStr = endDate.toIso8601String().split('T')[0];

      // Get supplier info
      final List<Map<String, dynamic>> supplierInfo = await db
          .rawQuery('SELECT * FROM suppliers WHERE id = ?', <int>[supplierId]);

      if (supplierInfo.isEmpty) {
        throw Exception('Supplier not found');
      }

      // Get purchases from this supplier
      final List<Map<String, dynamic>> purchases = await db.rawQuery(
          '''SELECT p.*, pr.name as product_name
           FROM purchases p
           LEFT JOIN products pr ON p.productId = pr.id
           WHERE p.supplierId = ? AND p.date BETWEEN ? AND ?
           ORDER BY p.date DESC''',
          <Object?>[supplierId, startDateStr, endDateStr]);

      // Calculate totals
      final double totalPurchases = purchases.fold(
          0.0,
          (double sum, Map<String, dynamic> purchase) =>
              sum + ((purchase['total'] ?? 0.0) as double));

      return <String, dynamic>{
        'supplier': supplierInfo.first,
        'period': <String, String>{
          'start': startDateStr,
          'end': endDateStr,
        },
        'summary': <String, dynamic>{
          'totalPurchases': totalPurchases,
          'totalOrders': purchases.length,
        },
        'transactions': purchases,
      };
    } catch (e) {
      throw Exception('Failed to get supplier statement: $e');
    }
  }
}
