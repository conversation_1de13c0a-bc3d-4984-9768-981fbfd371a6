/// Model class for Order (طلبية المحل)
class Order {
  /// Unique identifier for the order
  int? id;

  /// Customer ID for this order (legacy support)
  int? customerId;

  /// Date of the order
  String? date;

  /// Status of the order (pending, completed, cancelled, converted)
  String? status;

  /// Total estimated amount of the order
  double? total;

  /// Additional notes for the order
  String? notes;

  /// Type of order (manual, auto_low_stock, customer_order)
  String? type;

  /// Getter for totalAmount (alias for total)
  double? get totalAmount => total;

  /// Constructor for creating an Order instance
  Order({
    this.id,
    this.customerId,
    this.date,
    this.status,
    this.total,
    this.notes,
    this.type,
    double? totalAmount,
  }) {
    if (totalAmount != null) {
      total = totalAmount;
    }
  }

  /// Converts the Order instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'customerId': customerId,
      'date': date,
      'status': status,
      'total': total,
      'notes': notes,
      'type': type,
    };
  }

  /// Creates an Order instance from a Map (typically from database)
  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'] as int?,
      customerId: map['customerId'] as int?,
      date: map['date'] as String?,
      status: map['status'] as String?,
      total: map['total']?.toDouble(),
      notes: map['notes'] as String?,
      type: map['type'] as String?,
    );
  }

  @override
  String toString() {
    return 'Order{id: $id, customerId: $customerId, date: $date, '
        'status: $status, total: $total, notes: $notes, type: $type}';
  }
}
