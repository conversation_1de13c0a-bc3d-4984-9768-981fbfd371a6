/// أدوات مساعدة لتحديد نوع الشاشة والتنقل
class ScreenUtils {
  /// الشاشات الرئيسية التي يجب أن يظهر فيها شريط التنقل السفلي
  static const List<String> mainScreens = <String>[
    '/',
    '/dashboard',
    '/products',
    '/customers',
    '/sales',
    '/purchases',
    '/suppliers',
  ];

  /// الشاشات الثانوية التي يجب أن يختفي فيها شريط التنقل السفلي
  static const List<String> secondaryScreenPatterns = <String>[
    '/add',
    '/edit',
    '/details',
    '/form',
    '/settings',
    '/reports',
    '/analytics',
    '/backup',
    '/activities',
  ];

  /// تحديد ما إذا كانت الشاشة رئيسية
  static bool isMainScreen(String route) {
    // التحقق من الشاشات الرئيسية المحددة
    if (mainScreens.contains(route)) {
      return true;
    }

    // التحقق من أن المسار لا يحتوي على أنماط الشاشات الثانوية
    for (final String pattern in secondaryScreenPatterns) {
      if (route.contains(pattern)) {
        return false;
      }
    }

    // إذا كان المسار بسيط (مستوى واحد فقط) فهو رئيسي
    final List<String> pathSegments = route.split('/').where((String s) => s.isNotEmpty).toList();
    return pathSegments.length <= 1;
  }

  /// تحديد ما إذا كانت الشاشة تحتاج زر رجوع
  static bool needsBackButton(String route) {
    return !isMainScreen(route) && route != '/';
  }

  /// تحديد ما إذا كانت الشاشة تحتاج drawer
  static bool needsDrawer(String route) {
    return route == '/' || route == '/dashboard';
  }

  /// الحصول على عنوان الشاشة حسب المسار
  static String getScreenTitle(String route) {
    switch (route) {
      case '/':
      case '/dashboard':
        return 'أسامة ماركت';
      case '/products':
        return 'المنتجات';
      case '/products/add':
        return 'إضافة منتج';
      case '/customers':
        return 'العملاء';
      case '/customers/add':
        return 'إضافة عميل';
      case '/sales':
        return 'المبيعات';
      case '/sales/add':
        return 'بيع جديد';
      case '/purchases':
        return 'المشتريات';
      case '/purchases/add':
        return 'شراء جديد';
      case '/suppliers':
        return 'الموردين';
      case '/suppliers/add':
        return 'إضافة مورد';
      case '/reports':
        return 'التقارير';
      case '/analytics':
        return 'التحليلات';
      case '/settings':
        return 'الإعدادات';
      case '/backup':
        return 'النسخ الاحتياطي';
      case '/activities':
        return 'سجل النشاطات';
      default:
        return 'أسامة ماركت';
    }
  }
}
