import 'package:flutter/material.dart';

/// ودجت موحد لعرض الرسائل (نجاح، خطأ، تحذير، معلومات)
/// Unified widget for displaying messages (success, error, warning, info)
class MessageCard extends StatelessWidget {
  /// نص الرسالة
  final String message;
  
  /// لون الخلفية
  final Color backgroundColor;
  
  /// لون النص
  final Color textColor;
  
  /// أيقونة الرسالة
  final IconData icon;
  
  /// لون الأيقونة
  final Color iconColor;
  
  /// دالة الإغلاق (اختيارية)
  final VoidCallback? onClose;
  
  /// هل يمكن إغلاق الرسالة
  final bool dismissible;
  
  /// نوع الرسالة
  final MessageType type;

  const MessageCard({
    super.key,
    required this.message,
    this.backgroundColor = Colors.blue,
    this.textColor = Colors.white,
    this.icon = Icons.info,
    this.iconColor = Colors.white,
    this.onClose,
    this.dismissible = true,
    this.type = MessageType.info,
  });

  /// إنشاء رسالة نجاح
  factory MessageCard.success({
    required String message,
    VoidCallback? onClose,
    bool dismissible = true,
  }) {
    return MessageCard(
      message: message,
      backgroundColor: Colors.green,
      textColor: Colors.white,
      icon: Icons.check_circle,
      iconColor: Colors.white,
      onClose: onClose,
      dismissible: dismissible,
      type: MessageType.success,
    );
  }

  /// إنشاء رسالة خطأ
  factory MessageCard.error({
    required String message,
    VoidCallback? onClose,
    bool dismissible = true,
  }) {
    return MessageCard(
      message: message,
      backgroundColor: Colors.red,
      textColor: Colors.white,
      icon: Icons.error,
      iconColor: Colors.white,
      onClose: onClose,
      dismissible: dismissible,
      type: MessageType.error,
    );
  }

  /// إنشاء رسالة تحذير
  factory MessageCard.warning({
    required String message,
    VoidCallback? onClose,
    bool dismissible = true,
  }) {
    return MessageCard(
      message: message,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
      icon: Icons.warning,
      iconColor: Colors.white,
      onClose: onClose,
      dismissible: dismissible,
      type: MessageType.warning,
    );
  }

  /// إنشاء رسالة معلومات
  factory MessageCard.info({
    required String message,
    VoidCallback? onClose,
    bool dismissible = true,
  }) {
    return MessageCard(
      message: message,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
      icon: Icons.info,
      iconColor: Colors.white,
      onClose: onClose,
      dismissible: dismissible,
      type: MessageType.info,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: iconColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (dismissible && onClose != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onClose,
              child: Icon(
                Icons.close,
                color: iconColor,
                size: 20,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// أنواع الرسائل
enum MessageType {
  success,
  error,
  warning,
  info,
}

/// ودجت لعرض قائمة من الرسائل
class MessageList extends StatelessWidget {
  final List<MessageData> messages;
  final Function(int)? onDismiss;

  const MessageList({
    super.key,
    required this.messages,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    if (messages.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: messages.asMap().entries.map((entry) {
        final index = entry.key;
        final messageData = entry.value;
        
        return MessageCard(
          message: messageData.message,
          backgroundColor: messageData.backgroundColor,
          textColor: messageData.textColor,
          icon: messageData.icon,
          iconColor: messageData.iconColor,
          dismissible: messageData.dismissible,
          type: messageData.type,
          onClose: messageData.dismissible && onDismiss != null
              ? () => onDismiss!(index)
              : null,
        );
      }).toList(),
    );
  }
}

/// بيانات الرسالة
class MessageData {
  final String message;
  final Color backgroundColor;
  final Color textColor;
  final IconData icon;
  final Color iconColor;
  final bool dismissible;
  final MessageType type;

  const MessageData({
    required this.message,
    this.backgroundColor = Colors.blue,
    this.textColor = Colors.white,
    this.icon = Icons.info,
    this.iconColor = Colors.white,
    this.dismissible = true,
    this.type = MessageType.info,
  });

  /// إنشاء رسالة نجاح
  factory MessageData.success(String message, {bool dismissible = true}) {
    return MessageData(
      message: message,
      backgroundColor: Colors.green,
      textColor: Colors.white,
      icon: Icons.check_circle,
      iconColor: Colors.white,
      dismissible: dismissible,
      type: MessageType.success,
    );
  }

  /// إنشاء رسالة خطأ
  factory MessageData.error(String message, {bool dismissible = true}) {
    return MessageData(
      message: message,
      backgroundColor: Colors.red,
      textColor: Colors.white,
      icon: Icons.error,
      iconColor: Colors.white,
      dismissible: dismissible,
      type: MessageType.error,
    );
  }

  /// إنشاء رسالة تحذير
  factory MessageData.warning(String message, {bool dismissible = true}) {
    return MessageData(
      message: message,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
      icon: Icons.warning,
      iconColor: Colors.white,
      dismissible: dismissible,
      type: MessageType.warning,
    );
  }

  /// إنشاء رسالة معلومات
  factory MessageData.info(String message, {bool dismissible = true}) {
    return MessageData(
      message: message,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
      icon: Icons.info,
      iconColor: Colors.white,
      dismissible: dismissible,
      type: MessageType.info,
    );
  }
}
