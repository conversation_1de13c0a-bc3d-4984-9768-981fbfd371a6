import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/customer_statement_provider.dart';
import '../../providers/customer_provider.dart';
import '../../models/customer.dart';
import '../../config/app_colors.dart';
import '../dialogs/select_customer_dialog.dart';

class CustomerStatementScreen extends StatefulWidget {
  const CustomerStatementScreen({super.key});

  @override
  State<CustomerStatementScreen> createState() =>
      _CustomerStatementScreenState();
}

class _CustomerStatementScreenState extends State<CustomerStatementScreen> {
  final TextEditingController _customerController = TextEditingController();
  String _selectedPeriod = 'الكل';

  final List<String> _periodOptions = <String>[
    'الكل',
    'اليوم',
    'الأسبوع',
    'الشهر',
    'السنة',
    'تحديد يدوي',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CustomerProvider>().fetchCustomers();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('كشف حساب عميل'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Consumer<CustomerStatementProvider>(
          builder: (BuildContext context,
              CustomerStatementProvider statementProvider, Widget? child) {
            return Column(
              children: <Widget>[
                // Filter Section
                _buildFilterSection(statementProvider),

                // Content Section
                Expanded(
                  child: _buildContent(statementProvider),
                ),
              ],
            );
          },
        ),
        bottomNavigationBar: _buildBottomActions(),
      ),
    );
  }

  Widget _buildFilterSection(CustomerStatementProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Column(
        children: <Widget>[
          // Customer Selection
          TextFormField(
            controller: _customerController,
            decoration: InputDecoration(
              labelText: 'اختر العميل',
              prefixIcon: const Icon(Icons.person, color: Colors.blue),
              suffixIcon: IconButton(
                icon: const Icon(Icons.search),
                onPressed: _selectCustomer,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.blue),
              ),
            ),
            readOnly: true,
            onTap: _selectCustomer,
          ),

          const SizedBox(height: 16),

          // Period Selection
          Row(
            children: <Widget>[
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPeriod,
                  decoration: InputDecoration(
                    labelText: 'الفترة الزمنية',
                    prefixIcon:
                        const Icon(Icons.date_range, color: Colors.blue),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  items: _periodOptions.map((String period) {
                    return DropdownMenuItem(
                      value: period,
                      child: Text(period),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    if (value != null) {
                      setState(() {
                        _selectedPeriod = value;
                      });
                      if (value == 'تحديد يدوي') {
                        _showDateRangePicker(provider);
                      } else {
                        provider.filterByPeriod(value);
                      }
                    }
                  },
                ),
              ),

              const SizedBox(width: 12),

              // Generate Statement Button
              ElevatedButton.icon(
                onPressed: provider.selectedCustomer != null
                    ? () => provider.loadCustomerStatement()
                    : null,
                icon: const Icon(Icons.search),
                label: const Text('عرض الكشف'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent(CustomerStatementProvider provider) {
    if (provider.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.blue),
      );
    }

    if (provider.error != null) {
      return _buildErrorWidget(provider.error!);
    }

    if (provider.selectedCustomer == null) {
      return _buildEmptyState('يرجى اختيار عميل لعرض كشف الحساب');
    }

    if (provider.transactions.isEmpty) {
      return _buildEmptyState('لا توجد حركات للعميل في الفترة المحددة');
    }

    return Column(
      children: <Widget>[
        // Summary Section
        _buildSummarySection(provider),

        // Transactions List
        Expanded(
          child: _buildTransactionsList(provider),
        ),
      ],
    );
  }

  Widget _buildSummarySection(CustomerStatementProvider provider) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: <Widget>[
          Text(
            'ملخص حساب: ${provider.selectedCustomer!.name}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),

          const SizedBox(height: 12),

          // الصف الأول: إجمالي المدين والدائن
          Row(
            children: <Widget>[
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي المدين',
                  provider.formattedTotalDebit,
                  Colors.red,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي الدائن',
                  provider.formattedTotalCredit,
                  Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // الصف الثاني: تفاصيل الجملة والتجزئة
          Row(
            children: <Widget>[
              Expanded(
                child: _buildSummaryCard(
                  'دين الجملة',
                  provider.formattedTotalWholesaleDebit,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  'دين التجزئة',
                  provider.formattedTotalRetailDebit,
                  Colors.orange,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // الصف الثالث: الدين المتبقي والرصيد النهائي
          Row(
            children: <Widget>[
              Expanded(
                child: _buildSummaryCard(
                  'دين تجزئة متبقي',
                  provider.formattedRemainingRetailDebt,
                  Colors.purple,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  'الرصيد النهائي',
                  provider.formattedFinalBalance,
                  provider.finalBalance >= 0 ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: provider.finalBalance >= 0
                  ? Colors.green.shade100
                  : Colors.red.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: provider.finalBalance >= 0 ? Colors.green : Colors.red,
              ),
            ),
            child: Column(
              children: <Widget>[
                const Text(
                  'الرصيد النهائي',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  provider.formattedFinalBalance,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color:
                        provider.finalBalance >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: <Widget>[
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(CustomerStatementProvider provider) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: provider.transactions.length,
      itemBuilder: (BuildContext context, int index) {
        final CustomerStatementTransaction transaction =
            provider.transactions[index];
        return _buildTransactionCard(transaction);
      },
    );
  }

  Widget _buildTransactionCard(CustomerStatementTransaction transaction) {
    // تحديد اللون والأيقونة حسب نوع المعاملة
    Color cardColor;
    IconData iconData;

    if (transaction.description.contains('(جملة)')) {
      cardColor = AppColors.primary;
      iconData = Icons.business;
    } else if (transaction.description.contains('(تجزئة)')) {
      cardColor = AppColors.accent;
      iconData = Icons.shopping_cart;
    } else if (transaction.description.contains('دين تجزئة متبقي')) {
      cardColor = AppColors.reports;
      iconData = Icons.credit_card;
    } else if (transaction.description.contains('دفعة نقدية')) {
      cardColor = AppColors.success;
      iconData = Icons.payment;
    } else {
      cardColor = transaction.amountColor;
      iconData = transaction.type == 'debit'
          ? Icons.arrow_upward
          : Icons.arrow_downward;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: cardColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: cardColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                iconData,
                color: cardColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    transaction.description,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    transaction.formattedDate,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  // إضافة نوع المعاملة كتسمية
                  const SizedBox(height: 4),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: cardColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: cardColor.withOpacity(0.3)),
                    ),
                    child: Text(
                      _getTransactionTypeLabel(transaction.description),
                      style: TextStyle(
                        fontSize: 10,
                        color: cardColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: <Widget>[
                Text(
                  transaction.formattedAmount,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: cardColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'الرصيد: ${transaction.balance.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getTransactionTypeLabel(String description) {
    if (description.contains('(جملة)')) {
      return 'جملة';
    } else if (description.contains('(تجزئة)')) {
      return 'تجزئة';
    } else if (description.contains('دين تجزئة متبقي')) {
      return 'دين متبقي';
    } else if (description.contains('دفعة نقدية')) {
      return 'دفعة';
    } else {
      return 'عام';
    }
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Consumer<CustomerStatementProvider>(
      builder: (BuildContext context, CustomerStatementProvider provider,
          Widget? child) {
        if (provider.transactions.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            children: <Widget>[
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _printStatement(provider),
                  icon: const Icon(Icons.print),
                  label: const Text('طباعة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _saveAsPDF(provider),
                  icon: const Icon(Icons.picture_as_pdf),
                  label: const Text('حفظ PDF'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _shareStatement(provider),
                  icon: const Icon(Icons.share),
                  label: const Text('مشاركة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _selectCustomer() async {
    final Customer? customer = await showDialog<Customer>(
      context: context,
      builder: (BuildContext context) => const SelectCustomerDialog(),
    );

    if (customer != null) {
      setState(() {
        _customerController.text = customer.name ?? '';
      });
      context.read<CustomerStatementProvider>().setCustomer(customer);
    }
  }

  Future<void> _showDateRangePicker(CustomerStatementProvider provider) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: provider.startDate != null && provider.endDate != null
          ? DateTimeRange(start: provider.startDate!, end: provider.endDate!)
          : null,
    );

    if (picked != null) {
      provider.setDateRange(picked.start, picked.end);
    }
  }

  void _printStatement(CustomerStatementProvider provider) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة الطباعة قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _saveAsPDF(CustomerStatementProvider provider) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة حفظ PDF قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _shareStatement(CustomerStatementProvider provider) {
    final String statementText = provider.generateStatementText();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ كشف الحساب: ${statementText.length} حرف'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  void dispose() {
    _customerController.dispose();
    super.dispose();
  }
}
