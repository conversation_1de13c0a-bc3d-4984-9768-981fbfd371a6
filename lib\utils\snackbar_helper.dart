/// نظام معالجة الأخطاء والرسائل الموحد لتطبيق أسامة ماركت
///
/// يوفر طرق موحدة لعرض:
/// - رسائل النجاح (خضراء)
/// - رسائل الخطأ (حمراء)
/// - رسائل التحذير (برتقالية)
/// - رسائل المعلومات (زرقاء)
/// - رسائل التحميل (مع مؤشر دوار)
/// - حوارات مفصلة للأخطاء والنجاح
///
/// الاستخدام:
/// ```dart
/// SnackBarHelper.showSuccess(context, 'تم الحفظ بنجاح');
/// SnackBarHelper.showError(context, 'حدث خطأ في الحفظ');
/// DialogHelper.showError(context, title: 'خطأ', message: 'تفاصيل الخطأ');
/// ```

import 'package:flutter/material.dart';
import '../config/app_colors.dart';
import '../config/app_styles.dart';

/// كلاس مساعد لعرض الرسائل والإشعارات بشكل موحد
///
/// يوفر طرق ثابتة لعرض أنواع مختلفة من الرسائل
/// مع تصميم موحد ومتسق في جميع أنحاء التطبيق
class SnackBarHelper {
  /// عرض رسالة نجاح
  static void showSuccess(BuildContext context, String message) {
    _showSnackBar(
      context,
      message,
      AppColors.success,
      Icons.check_circle,
    );
  }

  /// عرض رسالة خطأ
  static void showError(BuildContext context, String message) {
    _showSnackBar(
      context,
      message,
      AppColors.error,
      Icons.error,
    );
  }

  /// عرض رسالة تحذير
  static void showWarning(BuildContext context, String message) {
    _showSnackBar(
      context,
      message,
      AppColors.warning,
      Icons.warning,
    );
  }

  /// عرض رسالة معلومات
  static void showInfo(BuildContext context, String message) {
    _showSnackBar(
      context,
      message,
      AppColors.info,
      Icons.info,
    );
  }

  /// عرض رسالة مخصصة
  static void showCustom(
    BuildContext context,
    String message, {
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
    Duration? duration,
    SnackBarAction? action,
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor ?? AppColors.primary,
      icon ?? Icons.notifications,
      textColor: textColor,
      duration: duration,
      action: action,
    );
  }

  /// عرض رسالة تحميل
  static void showLoading(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: <Widget>[
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                message,
                style: AppStyles.bodyMedium.copyWith(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// عرض رسالة مع إجراء
  static void showWithAction(
    BuildContext context,
    String message,
    String actionLabel,
    VoidCallback onActionPressed, {
    Color? backgroundColor,
    IconData? icon,
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor ?? AppColors.primary,
      icon ?? Icons.notifications,
      action: SnackBarAction(
        label: actionLabel,
        textColor: Colors.white,
        onPressed: onActionPressed,
      ),
    );
  }

  /// عرض رسالة تأكيد مع خيارات
  static void showConfirmation(
    BuildContext context,
    String message,
    VoidCallback onConfirm, {
    String confirmLabel = 'تأكيد',
    String cancelLabel = 'إلغاء',
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: AppStyles.bodyMedium.copyWith(color: Colors.white),
        ),
        backgroundColor: AppColors.warning,
        duration: const Duration(seconds: 5),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
        action: SnackBarAction(
          label: confirmLabel,
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            onConfirm();
          },
        ),
      ),
    );
  }

  /// إخفاء الرسالة الحالية
  static void hide(BuildContext context) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
  }

  /// إخفاء جميع الرسائل
  static void hideAll(BuildContext context) {
    ScaffoldMessenger.of(context).clearSnackBars();
  }

  /// الدالة الأساسية لعرض SnackBar
  static void _showSnackBar(
    BuildContext context,
    String message,
    Color backgroundColor,
    IconData icon, {
    Color? textColor,
    Duration? duration,
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: <Widget>[
            Icon(
              icon,
              color: textColor ?? Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: AppStyles.bodyMedium.copyWith(
                  color: textColor ?? Colors.white,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: duration ?? const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
        action: action,
      ),
    );
  }
}

/// فئة مساعدة لعرض الحوارات
class DialogHelper {
  /// عرض حوار تحميل
  static void showLoading(
    BuildContext context, {
    String message = 'جاري التحميل...',
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => AlertDialog(
        backgroundColor: AppColors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// إخفاء حوار التحميل
  static void hideLoading(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// عرض حوار معلومات
  static Future<void> showInfo(
    BuildContext context, {
    required String title,
    required String message,
    String buttonText = 'موافق',
  }) {
    return showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        backgroundColor: AppColors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: <Widget>[
            const Icon(
              Icons.info,
              color: AppColors.info,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: AppStyles.titleMedium,
            ),
          ],
        ),
        content: Text(
          message,
          style: AppStyles.bodyMedium,
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              buttonText,
              style: AppStyles.labelLarge.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض حوار خطأ
  static Future<void> showError(
    BuildContext context, {
    required String title,
    required String message,
    String buttonText = 'موافق',
  }) {
    return showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        backgroundColor: AppColors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: <Widget>[
            const Icon(
              Icons.error,
              color: AppColors.error,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.error,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: AppStyles.bodyMedium,
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              buttonText,
              style: AppStyles.labelLarge.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض حوار نجاح
  static Future<void> showSuccess(
    BuildContext context, {
    required String title,
    required String message,
    String buttonText = 'موافق',
  }) {
    return showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        backgroundColor: AppColors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: <Widget>[
            const Icon(
              Icons.check_circle,
              color: AppColors.success,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.success,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: AppStyles.bodyMedium,
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              buttonText,
              style: AppStyles.labelLarge.copyWith(
                color: AppColors.success,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
