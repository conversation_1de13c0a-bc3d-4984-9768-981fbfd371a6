/// Model class representing a backup record
class Backup {
  /// Unique identifier for the backup
  int? id;

  /// File path of the backup
  String? filePath;

  /// Date when the backup was created
  String? date;

  /// Type of backup (e.g., "full", "incremental")
  String? type;

  /// Constructor for creating a Backup instance
  Backup({
    this.id,
    this.filePath,
    this.date,
    this.type,
  });

  /// Converts the Backup instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'filePath': filePath,
      'date': date,
      'type': type,
    };
  }

  /// Creates a Backup instance from a Map (typically from database)
  factory Backup.fromMap(Map<String, dynamic> map) {
    return Backup(
      id: map['id'] as int?,
      filePath: map['filePath'] as String?,
      date: map['date'] as String?,
      type: map['type'] as String?,
    );
  }

  @override
  String toString() {
    return 'Backup{id: $id, filePath: $filePath, date: $date, type: $type}';
  }
}
