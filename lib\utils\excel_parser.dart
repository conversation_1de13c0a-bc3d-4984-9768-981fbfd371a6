import 'dart:io';
import 'dart:typed_data';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';

class ExcelParser {
  /// تحليل ملف إكسل وإرجاع البيانات كقائمة من الخرائط
  static Future<Map<String, dynamic>> parseExcelFile(PlatformFile file) async {
    try {
      if (file.bytes == null && file.path == null) {
        throw Exception('لا يمكن قراءة الملف المحدد');
      }

      // قراءة بيانات الملف
      Uint8List? bytes;
      if (file.bytes != null) {
        bytes = file.bytes!;
      } else if (file.path != null) {
        final File fileData = File(file.path!);
        bytes = await fileData.readAsBytes();
      }

      if (bytes == null) {
        throw Exception('فشل في قراءة بيانات الملف');
      }

      // تحديد نوع الملف ومعالجته
      if (file.extension?.toLowerCase() == 'csv') {
        return await _parseCsvData(bytes);
      } else if (file.extension?.toLowerCase() == 'xlsx' ||
          file.extension?.toLowerCase() == 'xls') {
        return await _parseExcelData(bytes);
      } else {
        throw Exception(
            'نوع الملف غير مدعوم. يرجى اختيار ملف CSV أو Excel (.xlsx/.xls)');
      }
    } catch (e) {
      throw Exception('خطأ في تحليل الملف: $e');
    }
  }

  /// تحليل بيانات Excel
  static Future<Map<String, dynamic>> _parseExcelData(Uint8List bytes) async {
    try {
      final Excel excel = Excel.decodeBytes(bytes);

      if (excel.tables.isEmpty) {
        throw Exception('الملف فارغ أو لا يحتوي على جداول');
      }

      // استخدام أول جدول في الملف
      final String tableName = excel.tables.keys.first;
      final Sheet table = excel.tables[tableName]!;

      if (table.rows.isEmpty) {
        throw Exception('الجدول فارغ');
      }

      // قراءة رؤوس الأعمدة من الصف الأول
      final List<Data?> headerRow = table.rows.first;
      final List<String> headers = <String>[];

      for (final Data? cell in headerRow) {
        final String value = cell?.value?.toString().trim() ?? '';
        if (value.isNotEmpty) {
          headers.add(value);
        }
      }

      if (headers.isEmpty) {
        throw Exception('لا توجد رؤوس أعمدة صالحة في الملف');
      }

      // قراءة بيانات الصفوف
      final List<Map<String, dynamic>> products = <Map<String, dynamic>>[];

      for (int i = 1; i < table.rows.length; i++) {
        final List<Data?> row = table.rows[i];
        final Map<String, dynamic> productData = <String, dynamic>{};

        // تخطي الصفوف الفارغة
        bool hasData = false;
        for (final Data? cell in row) {
          if (cell?.value != null && cell!.value.toString().trim().isNotEmpty) {
            hasData = true;
            break;
          }
        }

        if (!hasData) continue;

        // تعبئة بيانات المنتج
        for (int j = 0; j < headers.length && j < row.length; j++) {
          final String header = headers[j];
          final Data? cell = row[j];
          final CellValue? value = cell?.value;

          productData[header] = _parseValue(value, header);
        }

        // التحقق من وجود اسم المنتج على الأقل
        if (productData.containsKey('الاسم') ||
            productData.containsKey('اسم المنتج') ||
            productData.containsKey('name') ||
            productData.containsKey('product_name')) {
          products.add(productData);
        }
      }

      return <String, dynamic>{
        'success': true,
        'headers': headers,
        'products': products,
        'totalRows': products.length,
        'fileName': 'Excel File',
      };
    } catch (e) {
      throw Exception('خطأ في تحليل ملف Excel: $e');
    }
  }

  /// تحليل بيانات CSV
  static Future<Map<String, dynamic>> _parseCsvData(Uint8List bytes) async {
    try {
      final String content = String.fromCharCodes(bytes);
      final List<String> lines = content.split('\n');

      if (lines.isEmpty) {
        throw Exception('الملف فارغ');
      }

      // قراءة رؤوس الأعمدة
      final String headerLine = lines.first.trim();
      if (headerLine.isEmpty) {
        throw Exception('لا توجد رؤوس أعمدة في الملف');
      }

      final List<String> headers = headerLine
          .split(',')
          .map((String h) => h.trim().replaceAll('"', ''))
          .toList();

      // قراءة بيانات الصفوف
      final List<Map<String, dynamic>> products = <Map<String, dynamic>>[];

      for (int i = 1; i < lines.length; i++) {
        final String line = lines[i].trim();
        if (line.isEmpty) continue;

        final List<String> values = line
            .split(',')
            .map((String v) => v.trim().replaceAll('"', ''))
            .toList();
        final Map<String, dynamic> productData = <String, dynamic>{};

        for (int j = 0; j < headers.length && j < values.length; j++) {
          final String header = headers[j];
          final String value = values[j];

          productData[header] = _parseValue(value, header);
        }

        // التحقق من وجود اسم المنتج
        if (productData.containsKey('الاسم') ||
            productData.containsKey('اسم المنتج') ||
            productData.containsKey('name') ||
            productData.containsKey('product_name')) {
          products.add(productData);
        }
      }

      return <String, dynamic>{
        'success': true,
        'headers': headers,
        'products': products,
        'totalRows': products.length,
        'fileName': 'CSV File',
      };
    } catch (e) {
      throw Exception('خطأ في تحليل ملف CSV: $e');
    }
  }

  /// تحويل القيمة إلى النوع المناسب بناءً على رأس العمود
  static dynamic _parseValue(dynamic value, String header) {
    if (value == null) return null;

    final String stringValue = value.toString().trim();
    if (stringValue.isEmpty) return null;

    final String lowerHeader = header.toLowerCase();

    // الحقول الرقمية
    if (lowerHeader.contains('سعر') ||
        lowerHeader.contains('price') ||
        lowerHeader.contains('كمية') ||
        lowerHeader.contains('quantity') ||
        lowerHeader.contains('رصيد') ||
        lowerHeader.contains('balance')) {
      try {
        // محاولة تحويل إلى رقم عشري
        return double.parse(stringValue.replaceAll(',', ''));
      } catch (e) {
        try {
          // محاولة تحويل إلى رقم صحيح
          return int.parse(stringValue.replaceAll(',', ''));
        } catch (e) {
          // إرجاع القيمة كما هي إذا فشل التحويل
          return stringValue;
        }
      }
    }

    // الحقول المنطقية
    if (lowerHeader.contains('نشط') ||
        lowerHeader.contains('active') ||
        lowerHeader.contains('متاح') ||
        lowerHeader.contains('available')) {
      final String lowerValue = stringValue.toLowerCase();
      return lowerValue == 'true' ||
          lowerValue == 'نعم' ||
          lowerValue == 'yes' ||
          lowerValue == '1' ||
          lowerValue == 'صحيح';
    }

    // إرجاع النص كما هو
    return stringValue;
  }

  /// التحقق من صحة رؤوس الأعمدة المطلوبة
  static Map<String, String> validateHeaders(List<String> headers) {
    final Map<String, String> mapping = <String, String>{};
    final Map<String, List<String>> expectedHeaders = <String, List<String>>{
      'name': <String>['الاسم', 'اسم المنتج', 'name', 'product_name', 'اسم'],
      'unit': <String>['الوحدة', 'وحدة القياس', 'unit', 'unit_name', 'وحدة'],
      'wholesalePrice': <String>[
        'سعر الجملة',
        'wholesale_price',
        'سعر جملة',
        'جملة'
      ],
      'retailPrice': <String>[
        'سعر التجزئة',
        'retail_price',
        'سعر تجزئة',
        'تجزئة'
      ],
      'purchasePrice': <String>[
        'سعر الشراء',
        'purchase_price',
        'سعر شراء',
        'شراء'
      ],
      'warehouseQuantity': <String>[
        'كمية المخزن',
        'warehouse_quantity',
        'مخزن',
        'كمية مخزن'
      ],
      'storeQuantity': <String>[
        'كمية البقالة',
        'store_quantity',
        'بقالة',
        'كمية بقالة'
      ],
      'category': <String>['الفئة', 'التصنيف', 'category', 'فئة'],
      'barcode': <String>['الباركود', 'barcode', 'رمز المنتج'],
    };

    for (final String header in headers) {
      final String lowerHeader = header.toLowerCase().trim();

      for (final MapEntry<String, List<String>> entry
          in expectedHeaders.entries) {
        final String fieldName = entry.key;
        final List<String> possibleNames = entry.value;

        for (final String possibleName in possibleNames) {
          if (lowerHeader == possibleName.toLowerCase() ||
              lowerHeader.contains(possibleName.toLowerCase())) {
            mapping[fieldName] = header;
            break;
          }
        }
      }
    }

    return mapping;
  }

  /// إنشاء ملف Excel نموذجي للتحميل
  static Future<Uint8List> createSampleExcelFile() async {
    final Excel excel = Excel.createExcel();
    final Sheet sheet = excel['المنتجات'];

    // إضافة رؤوس الأعمدة
    final List<String> headers = <String>[
      'الاسم',
      'الوحدة',
      'سعر الجملة',
      'سعر التجزئة',
      'سعر الشراء',
      'كمية المخزن',
      'كمية البقالة',
      'الفئة',
      'الباركود'
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
    }

    // إضافة بيانات نموذجية
    final List<List<Object>> sampleData = <List<Object>>[
      <Object>[
        'أرز بسمتي',
        'كيس',
        25.0,
        30.0,
        20.0,
        100,
        50,
        'حبوب',
        '1234567890'
      ],
      <Object>[
        'زيت زيتون',
        'زجاجة',
        45.0,
        55.0,
        40.0,
        80,
        30,
        'زيوت',
        '2345678901'
      ],
      <Object>[
        'سكر أبيض',
        'كيس',
        15.0,
        18.0,
        12.0,
        200,
        100,
        'سكريات',
        '3456789012'
      ],
    ];

    for (int row = 0; row < sampleData.length; row++) {
      for (int col = 0; col < sampleData[row].length; col++) {
        final Object value = sampleData[row][col];
        if (value is String) {
          sheet
              .cell(CellIndex.indexByColumnRow(
                  columnIndex: col, rowIndex: row + 1))
              .value = TextCellValue(value);
        } else if (value is num) {
          sheet
              .cell(CellIndex.indexByColumnRow(
                  columnIndex: col, rowIndex: row + 1))
              .value = DoubleCellValue(value.toDouble());
        }
      }
    }

    final List<int>? encoded = excel.encode();
    return Uint8List.fromList(encoded ?? <int>[]);
  }
}
