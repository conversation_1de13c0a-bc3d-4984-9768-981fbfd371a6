import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/unit_provider.dart';
import 'package:inventory_management_app/models/unit.dart';

class UnitDetailsScreen extends StatefulWidget {
  final Unit? unit;

  const UnitDetailsScreen({super.key, this.unit});

  @override
  _UnitDetailsScreenState createState() => _UnitDetailsScreenState();
}

class _UnitDetailsScreenState extends State<UnitDetailsScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late String _name;
  late String _symbol;

  @override
  void initState() {
    super.initState();
    _name = widget.unit?.name ?? '';
    _symbol = widget.unit?.symbol ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.unit == null ? 'Add Unit' : 'Edit Unit'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              TextFormField(
                initialValue: _name,
                decoration: const InputDecoration(labelText: 'Name'),
                validator: (String? value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
                onSaved: (String? value) => _name = value!,
              ),
              TextFormField(
                initialValue: _symbol,
                decoration: const InputDecoration(labelText: 'Symbol'),
                validator: (String? value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a symbol';
                  }
                  return null;
                },
                onSaved: (String? value) => _symbol = value!,
              ),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    final Unit unit = Unit(
                      id: widget.unit?.id,
                      name: _name,
                      symbol: _symbol,
                    );
                    final UnitProvider unitProvider =
                        Provider.of<UnitProvider>(context, listen: false);
                    if (widget.unit == null) {
                      unitProvider.addUnit(unit);
                    } else {
                      unitProvider.updateUnit(unit);
                    }
                    Navigator.pop(context);
                  }
                },
                child: const Text('Save'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
