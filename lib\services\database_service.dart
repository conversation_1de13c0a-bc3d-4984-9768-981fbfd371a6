import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:path/path.dart';
import 'package:inventory_management_app/data/database_helper.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static DatabaseService get instance => _instance;
  static Database? _database;

  // متغير للتحكم في وضع الاختبار
  static bool _isTestMode = false;
  static String? _testDatabasePath;

  /// الحصول على قاعدة البيانات (عادية أو للاختبار)
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initializeDatabase();
    return _database!;
  }

  /// تفعيل وضع الاختبار مع قاعدة بيانات في الذاكرة
  static void enableTestMode({String? testDatabasePath}) {
    _isTestMode = true;
    _testDatabasePath = testDatabasePath ?? ':memory:';
  }

  /// إلغاء تفعيل وضع الاختبار
  static void disableTestMode() {
    _isTestMode = false;
    _testDatabasePath = null;
  }

  /// تنظيف قاعدة البيانات للاختبار
  Future<void> clearDatabaseForTesting() async {
    if (!_isTestMode) return;

    final Database db = await database;

    // حذف جميع البيانات من الجداول
    await db.delete('products');
    await db.delete('customers');
    await db.delete('suppliers');
    await db.delete('sales');
    await db.delete('sale_items');
    await db.delete('purchases');
    await db.delete('purchase_items');
    await db.delete('orders');
    await db.delete('order_items');
    await db.delete('expenses');
    await db.delete('activities');
    await db.delete('transactions');
    await db.delete('internal_transfers');
    await db.delete('store_inventory_adjustments');
  }

  /// إعادة تعيين قاعدة البيانات (إغلاق الاتصال)
  Future<void> resetDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  Future<Database> _initializeDatabase() async {
    // Initialize database factory for web
    if (kIsWeb) {
      databaseFactory = databaseFactoryFfiWeb;
    }

    String path;
    if (_isTestMode && _testDatabasePath != null) {
      // استخدام قاعدة بيانات الاختبار
      path = _testDatabasePath!;
    } else {
      // استخدام قاعدة البيانات العادية
      final String databasePath = await getDatabasesPath();
      path = join(databasePath, 'inventory_management.db');
    }

    return openDatabase(
      path,
      version: 7, // تحديث مخططات الجداول لتتوافق مع النماذج
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    try {
      print('🔄 Creating database tables (version $version)...');

      // إنشاء الجداول الأساسية
      await db.execute(createProductsTable);
      await db.execute(createCustomersTable);
      await db.execute(createSuppliersTable);
      await db.execute(createCategoriesTable);
      await db.execute(createUnitsTable);
      print('✅ Created basic tables');

      // إنشاء جداول المعاملات
      await db.execute(createOrdersTable);
      await db.execute(createOrderItemsTable);
      await db.execute(createSalesTable);
      await db.execute(createSaleItemsTable);
      await db.execute(createPurchasesTable);
      await db.execute(createPurchaseItemsTable);
      print('✅ Created transaction tables');

      // إنشاء الجداول المساعدة
      await db.execute(createExpensesTable);
      await db.execute(createBackupsTable);
      await db.execute(createActivitiesTable);
      await db.execute(createTransactionsTable);
      await db.execute(createDailySummaryTable);
      await db.execute(createCustomerStatementTable);
      await db.execute(createSupplierStatementTable);
      print('✅ Created auxiliary tables');

      // إنشاء الجداول المتقدمة
      await db.execute(createInternalTransfersTable);
      await db.execute(createStoreInventoryAdjustmentsTable);
      print('✅ Created advanced tables');

      // إنشاء الفهارس
      await _createIndexes(db);

      print('🎉 Database created successfully with version $version');
    } catch (e) {
      print('❌ Error creating database: $e');
      rethrow;
    }
  }

  /// Handle database upgrades safely without losing data
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('🔄 Upgrading database from version $oldVersion to $newVersion');

    if (oldVersion < 2) {
      // Upgrade from version 1 to 2: Fix expenses table structure
      await _upgradeToVersion2(db);
    }

    if (oldVersion < 3) {
      // Upgrade from version 2 to 3: Add warehouse and store system support
      await _upgradeToVersion3(db);
    }

    if (oldVersion < 4) {
      // Upgrade from version 3 to 4: Add internal transfers support
      await _upgradeToVersion4(db);
    }

    if (oldVersion < 5) {
      // Upgrade from version 4 to 5: Add store inventory adjustments support
      await _upgradeToVersion5(db);
    }

    if (oldVersion < 6) {
      // Upgrade from version 5 to 6: Fix duplicate indexes issue
      await _upgradeToVersion6(db);
    }

    if (oldVersion < 7) {
      // Upgrade from version 6 to 7: Update table schemas to match models
      await _upgradeToVersion7(db);
    }

    // Future upgrades can be added here
    // if (oldVersion < 8) {
    //   await _upgradeToVersion8(db);
    // }
  }

  /// Upgrade to version 2: Fix expenses table structure
  Future<void> _upgradeToVersion2(Database db) async {
    try {
      print('📊 Upgrading expenses table structure...');

      // Step 1: Check if old expenses table exists and has data
      final List<Map<String, dynamic>> existingExpenses = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'");

      List<Map<String, dynamic>> oldExpensesData = <Map<String, dynamic>>[];
      if (existingExpenses.isNotEmpty) {
        // Backup existing data
        try {
          oldExpensesData = await db.query('expenses');
          print('💾 Backed up ${oldExpensesData.length} existing expenses');
        } catch (e) {
          print('⚠️ No existing expenses data to backup: $e');
        }
      }

      // Step 2: Drop old expenses table if exists
      await db.execute('DROP TABLE IF EXISTS expenses');
      print('🗑️ Dropped old expenses table');

      // Step 3: Create new expenses table with correct structure
      await db.execute(createExpensesTable);
      print('✅ Created new expenses table');

      // Step 4: Migrate old data if any exists
      if (oldExpensesData.isNotEmpty) {
        await _migrateExpensesData(db, oldExpensesData);
      }

      print('🎉 Successfully upgraded expenses table to version 2');
    } catch (e) {
      print('❌ Error upgrading to version 2: $e');
      // If upgrade fails, recreate the table with new structure
      await db.execute('DROP TABLE IF EXISTS expenses');
      await db.execute(createExpensesTable);
      print('🔧 Recreated expenses table after upgrade error');
    }
  }

  /// Upgrade to version 3: Add warehouse and store system support
  Future<void> _upgradeToVersion3(Database db) async {
    try {
      print('🏪 Upgrading to warehouse and store system (version 3)...');

      // Add new columns to products table
      await db.execute(
          'ALTER TABLE products ADD COLUMN retailPrice REAL DEFAULT 0');
      await db.execute(
          'ALTER TABLE products ADD COLUMN warehouseQuantity INTEGER DEFAULT 0');
      await db.execute(
          'ALTER TABLE products ADD COLUMN storeQuantity INTEGER DEFAULT 0');
      print('✅ Added new columns to products table');

      // Add new columns to sales table
      await db.execute(
          'ALTER TABLE sales ADD COLUMN totalWholesaleAmount REAL DEFAULT 0');
      await db.execute(
          'ALTER TABLE sales ADD COLUMN totalRetailAmount REAL DEFAULT 0');
      await db.execute(
          'ALTER TABLE sales ADD COLUMN remainingRetailAmount REAL DEFAULT 0');
      await db.execute('ALTER TABLE sales ADD COLUMN notesForRetailItems TEXT');
      print('✅ Added new columns to sales table');

      // Add new column to sale_items table
      await db.execute(
          'ALTER TABLE sale_items ADD COLUMN itemType TEXT DEFAULT \'wholesale\'');
      print('✅ Added itemType column to sale_items table');

      // Migrate existing quantity data to warehouse
      await db.execute('''
        UPDATE products
        SET warehouseQuantity = COALESCE(quantity, 0),
            storeQuantity = 0
        WHERE warehouseQuantity IS NULL OR warehouseQuantity = 0
      ''');
      print('📦 Migrated existing quantities to warehouse');

      // Set default retail prices (same as wholesale for now)
      await db.execute('''
        UPDATE products
        SET retailPrice = COALESCE(price, 0)
        WHERE retailPrice IS NULL OR retailPrice = 0
      ''');
      print('💰 Set default retail prices');

      print(
          '🎉 Successfully upgraded to version 3 (warehouse and store system)');
    } catch (e) {
      print('❌ Error upgrading to version 3: $e');
      rethrow;
    }
  }

  /// Upgrade to version 4: Add internal transfers support
  Future<void> _upgradeToVersion4(Database db) async {
    try {
      print('🔄 Upgrading to internal transfers system (version 4)...');

      // Create internal_transfers table
      await db.execute(createInternalTransfersTable);
      print('✅ Created internal_transfers table');

      // Note: Indexes will be created by _createIndexes() if needed

      print(
          '🎉 Successfully upgraded to version 4 (internal transfers system)');
    } catch (e) {
      print('❌ Error upgrading to version 4: $e');
      rethrow;
    }
  }

  /// Upgrade to version 5: Add store inventory adjustments support
  Future<void> _upgradeToVersion5(Database db) async {
    try {
      print(
          '🔄 Upgrading to store inventory adjustments system (version 5)...');

      // Create store_inventory_adjustments table
      await db.execute(createStoreInventoryAdjustmentsTable);
      print('✅ Created store_inventory_adjustments table');

      // Note: Indexes will be created by _createIndexes() if needed

      print(
          '🎉 Successfully upgraded to version 5 (store inventory adjustments system)');
    } catch (e) {
      print('❌ Error upgrading to version 5: $e');
      rethrow;
    }
  }

  /// Upgrade to version 6: Fix duplicate indexes issue
  Future<void> _upgradeToVersion6(Database db) async {
    try {
      print('🔧 Upgrading to fix indexes issue (version 6)...');

      // إعادة إنشاء جميع الفهارس بشكل صحيح
      await _createIndexes(db);

      print('🎉 Successfully upgraded to version 6 (fixed indexes issue)');
    } catch (e) {
      print('❌ Error upgrading to version 6: $e');
      // لا نرمي الخطأ هنا لأن الفهارس ليست حرجة للتشغيل
      print('⚠️ Continuing without some indexes...');
    }
  }

  /// Upgrade to version 7: Update table schemas to match models
  Future<void> _upgradeToVersion7(Database db) async {
    try {
      print('🔄 Upgrading table schemas to match models (version 7)...');

      // For development, we'll recreate tables with new schemas
      // In production, you might want to use ALTER TABLE statements

      // Backup existing data
      final List<Map<String, dynamic>> existingProducts = [];
      final List<Map<String, dynamic>> existingCustomers = [];
      final List<Map<String, dynamic>> existingSales = [];

      try {
        existingProducts.addAll(await db.query('products'));
        existingCustomers.addAll(await db.query('customers'));
        existingSales.addAll(await db.query('sales'));
        print('💾 Backed up existing data');
      } catch (e) {
        print('⚠️ Error backing up data: $e');
      }

      // Drop and recreate tables with new schemas
      await db.execute('DROP TABLE IF EXISTS products');
      await db.execute('DROP TABLE IF EXISTS customers');
      await db.execute('DROP TABLE IF EXISTS sales');

      // Create tables with new schemas
      await db.execute(createProductsTable);
      await db.execute(createCustomersTable);
      await db.execute(createSalesTable);
      print('✅ Recreated tables with new schemas');

      // Migrate existing data
      await _migrateProductsToVersion7(db, existingProducts);
      await _migrateCustomersToVersion7(db, existingCustomers);
      await _migrateSalesToVersion7(db, existingSales);

      print('🎉 Successfully upgraded to version 7 (updated schemas)');
    } catch (e) {
      print('❌ Error upgrading to version 7: $e');
      rethrow;
    }
  }

  /// Migrate products data to version 7 schema
  Future<void> _migrateProductsToVersion7(
      Database db, List<Map<String, dynamic>> oldData) async {
    try {
      for (final Map<String, dynamic> oldProduct in oldData) {
        final Map<String, dynamic> newProduct = <String, dynamic>{
          'id': oldProduct['id'],
          'name': oldProduct['name'],
          'description': oldProduct['description'] ?? '',
          'price': oldProduct['price'],
          'quantity': oldProduct['quantity'],
          'categoryId': oldProduct['categoryId'],
          'unitId': oldProduct['unitId'],
          'supplierId': oldProduct['supplierId'],
          'category': oldProduct['category'],
          'unit': oldProduct['unit'],
          'purchasePrice': oldProduct['purchasePrice'],
          'salePrice': oldProduct['salePrice'],
          'minLevel': oldProduct['minLevel'],
          'barcode': oldProduct['barcode'],
          'createdAt':
              oldProduct['createdAt'] ?? DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'date': oldProduct['date'],
          'retailPrice': oldProduct['retailPrice'],
          'warehouseQuantity': oldProduct['warehouseQuantity'] ?? 0,
          'storeQuantity': oldProduct['storeQuantity'] ?? 0,
        };

        await db.insert('products', newProduct);
      }
      print('📦 Migrated ${oldData.length} products to new schema');
    } catch (e) {
      print('⚠️ Error migrating products data: $e');
    }
  }

  /// Migrate customers data to version 7 schema
  Future<void> _migrateCustomersToVersion7(
      Database db, List<Map<String, dynamic>> oldData) async {
    try {
      for (final Map<String, dynamic> oldCustomer in oldData) {
        final Map<String, dynamic> newCustomer = <String, dynamic>{
          'id': oldCustomer['id'],
          'name': oldCustomer['name'],
          'email': oldCustomer['email'],
          'phone': oldCustomer['phone'],
          'address': oldCustomer['address'],
          'balance': oldCustomer['balance'] ?? 0.0,
          'created_at':
              oldCustomer['created_at'] ?? DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
          'notes': oldCustomer['notes'],
        };

        await db.insert('customers', newCustomer);
      }
      print('📦 Migrated ${oldData.length} customers to new schema');
    } catch (e) {
      print('⚠️ Error migrating customers data: $e');
    }
  }

  /// Migrate sales data to version 7 schema
  Future<void> _migrateSalesToVersion7(
      Database db, List<Map<String, dynamic>> oldData) async {
    try {
      for (final Map<String, dynamic> oldSale in oldData) {
        final Map<String, dynamic> newSale = <String, dynamic>{
          'id': oldSale['id'],
          'customerId': oldSale['customerId'],
          'date': oldSale['date'],
          'total': oldSale['total'],
          'notes': oldSale['notes'],
          'status': oldSale['status'],
          'invoiceNumber': oldSale['invoiceNumber'],
          'customerName': oldSale['customerName'],
          'paymentMethod': oldSale['paymentMethod'],
          'totalWholesaleAmount': oldSale['totalWholesaleAmount'],
          'totalRetailAmount': oldSale['totalRetailAmount'],
          'remainingRetailAmount': oldSale['remainingRetailAmount'],
          'notesForRetailItems': oldSale['notesForRetailItems'],
          'paidAmount': oldSale['paidAmount'] ?? 0.0,
          'remainingAmount': oldSale['remainingAmount'] ?? 0.0,
          'saleType': oldSale['saleType'],
          'createdAt': oldSale['createdAt'] ?? DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        };

        await db.insert('sales', newSale);
      }
      print('📦 Migrated ${oldData.length} sales to new schema');
    } catch (e) {
      print('⚠️ Error migrating sales data: $e');
    }
  }

  /// Migrate old expenses data to new table structure
  Future<void> _migrateExpensesData(
      Database db, List<Map<String, dynamic>> oldData) async {
    try {
      for (final Map<String, dynamic> oldExpense in oldData) {
        // Convert old structure to new structure
        final Map<String, dynamic> newExpense = <String, dynamic>{
          'expenseDate': oldExpense['date'], // date -> expenseDate
          'category': _convertCategoryIdToString(
              oldExpense['categoryId']), // categoryId -> category
          'amount': oldExpense['amount'],
          'description':
              oldExpense['notes'] ?? '', // notes -> description (fallback)
          'notes': oldExpense['notes'],
          'status': 'active', // default status
        };

        await db.insert('expenses', newExpense);
      }
      print('📦 Migrated ${oldData.length} expenses to new structure');
    } catch (e) {
      print('⚠️ Error migrating expenses data: $e');
    }
  }

  /// Convert old categoryId to new category string
  String _convertCategoryIdToString(dynamic categoryId) {
    if (categoryId == null) return 'miscellaneous';

    // Map old category IDs to new category strings
    switch (categoryId) {
      case 1:
        return 'rent';
      case 2:
        return 'salaries';
      case 3:
        return 'supplies';
      case 4:
        return 'utilities';
      case 5:
        return 'maintenance';
      case 6:
        return 'marketing';
      case 7:
        return 'transportation';
      default:
        return 'miscellaneous';
    }
  }

  /// Create performance indexes for frequently queried columns
  Future<void> _createIndexes(Database db) async {
    // استخدام الفهارس الموحدة من database_helper.dart
    for (final String index in createIndexes) {
      try {
        await db.execute(index);
      } catch (e) {
        print('⚠️ Warning creating index: $e');
        // Continue with other indexes even if one fails
      }
    }
    print('✅ All database indexes created successfully');
  }

  /// Delete database (for testing purposes only)
  /// ⚠️ WARNING: This will delete all data permanently!
  Future<void> deleteDatabaseForTesting() async {
    try {
      final String databasePath = await getDatabasesPath();
      final String path = join(databasePath, 'inventory_management.db');

      // Close current database connection
      if (_database != null) {
        await _database!.close();
        _database = null;
      }

      // Delete the database file
      await deleteDatabase(path);
      print('🗑️ Database deleted successfully for testing');
    } catch (e) {
      print('❌ Error deleting database: $e');
    }
  }

  /// Get database file path
  Future<String> getDatabaseFilePath() async {
    final String databasePath = await getDatabasesPath();
    return join(databasePath, 'inventory_management.db');
  }

  /// Restore database from a backup file
  Future<void> restoreDatabaseFromFile(String backupFilePath) async {
    try {
      final String currentDbPath = await getDatabaseFilePath();

      // Close current database connection
      await resetDatabase();

      // Copy backup file to current database location
      final File backupFile = File(backupFilePath);

      if (!await backupFile.exists()) {
        throw Exception('Backup file does not exist: $backupFilePath');
      }

      await backupFile.copy(currentDbPath);

      print('✅ Database restored successfully from $backupFilePath');
    } catch (e) {
      print('❌ Error restoring database from file: $e');
      rethrow;
    }
  }

  /// Get database file path
  Future<String> getDatabasePath() async {
    final String databasePath = await getDatabasesPath();
    return join(databasePath, 'inventory_management.db');
  }

  // ==================== Internal Transfers CRUD ====================

  /// Get all internal transfers
  Future<List<Map<String, dynamic>>> getInternalTransfers() async {
    final Database db = await database;
    return await db.query('internal_transfers', orderBy: 'date DESC');
  }

  /// Insert internal transfer
  Future<int> insertInternalTransfer(Map<String, dynamic> transfer) async {
    final Database db = await database;
    return await db.insert('internal_transfers', transfer);
  }

  /// Update internal transfer
  Future<void> updateInternalTransfer(Map<String, dynamic> transfer) async {
    final Database db = await database;
    await db.update(
      'internal_transfers',
      transfer,
      where: 'id = ?',
      whereArgs: <dynamic>[transfer['id']],
    );
  }

  /// Delete internal transfer
  Future<void> deleteInternalTransfer(int id) async {
    final Database db = await database;
    await db.delete(
      'internal_transfers',
      where: 'id = ?',
      whereArgs: <int>[id],
    );
  }

  /// Get internal transfers by date range
  Future<List<Map<String, dynamic>>> getInternalTransfersByDateRange(
      DateTime startDate, DateTime endDate) async {
    final Database db = await database;
    return await db.query(
      'internal_transfers',
      where: 'date BETWEEN ? AND ?',
      whereArgs: <String>[
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'date DESC',
    );
  }

  /// Get internal transfers by product
  Future<List<Map<String, dynamic>>> getInternalTransfersByProduct(
      int productId) async {
    final Database db = await database;
    return await db.query(
      'internal_transfers',
      where: 'product_id = ?',
      whereArgs: <int>[productId],
      orderBy: 'date DESC',
    );
  }

  /// Get total transferred quantity for a product
  Future<int> getTotalTransferredQuantity(int productId) async {
    final Database db = await database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT SUM(quantity) as total FROM internal_transfers WHERE product_id = ?',
      <int>[productId],
    );
    return result.first['total'] as int? ?? 0;
  }

  /// Get internal transfers count
  Future<int> getInternalTransfersCount() async {
    final Database db = await database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM internal_transfers',
    );
    return result.first['count'] as int? ?? 0;
  }

  // ==================== Store Inventory Adjustments CRUD ====================

  /// Get all store inventory adjustments
  Future<List<Map<String, dynamic>>> getStoreInventoryAdjustments() async {
    final Database db = await database;
    return await db.query('store_inventory_adjustments', orderBy: 'date DESC');
  }

  /// Insert store inventory adjustment
  Future<int> insertStoreInventoryAdjustment(
      Map<String, dynamic> adjustment) async {
    final Database db = await database;
    return await db.insert('store_inventory_adjustments', adjustment);
  }

  /// Get store inventory adjustments by date range
  Future<List<Map<String, dynamic>>> getStoreInventoryAdjustmentsByDateRange(
      DateTime startDate, DateTime endDate) async {
    final Database db = await database;
    return await db.query(
      'store_inventory_adjustments',
      where: 'date BETWEEN ? AND ?',
      whereArgs: <String>[
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'date DESC',
    );
  }

  /// Get total adjustment value
  Future<double> getTotalAdjustmentValue(
      DateTime startDate, DateTime endDate) async {
    final Database db = await database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT SUM(difference * retail_price) as total FROM store_inventory_adjustments WHERE date BETWEEN ? AND ?',
      <String>[
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
    );
    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  /// Get negative adjustment value (losses)
  Future<double> getNegativeAdjustmentValue(
      DateTime startDate, DateTime endDate) async {
    final Database db = await database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT SUM(difference * retail_price) as total FROM store_inventory_adjustments WHERE difference < 0 AND date BETWEEN ? AND ?',
      <String>[
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
    );
    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }
}
