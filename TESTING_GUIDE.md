# دليل نظام الاختبارات - أسامة ماركت

## 🧪 **نظرة عامة على نظام الاختبارات**

تم تطوير نظام اختبارات شامل يغطي جميع جوانب التطبيق لضمان الجودة والموثوقية.

### **📁 ملفات الاختبار:**
- `widget_test.dart` - اختبارات الواجهة
- `unit_tests.dart` - اختبارات الوحدة
- `provider_tests.dart` - اختبارات المزودات
- `service_tests.dart` - اختبارات الخدمات
- `test_runner.dart` - مشغل الاختبارات الشامل

## 🚀 **كيفية تشغيل الاختبارات**

### **1. تشغيل جميع الاختبارات:**
```bash
flutter test
```

### **2. تشغيل اختبارات محددة:**
```bash
# اختبارات الواجهة
flutter test test/widget_test.dart

# اختبارات الوحدة
flutter test test/unit_tests.dart

# اختبارات المزودات
flutter test test/provider_tests.dart

# اختبارات الخدمات
flutter test test/service_tests.dart

# الاختبارات الشاملة
flutter test test/test_runner.dart
```

### **3. تشغيل مع تقرير التغطية:**
```bash
flutter test --coverage
```

### **4. تشغيل مع تفاصيل إضافية:**
```bash
flutter test --verbose
```

## 📊 **أنواع الاختبارات**

### **🎨 اختبارات الواجهة (Widget Tests)**
**الهدف:** التأكد من عمل عناصر الواجهة بشكل صحيح

**ما يتم اختباره:**
- شاشة البداية (Splash Screen)
- الشاشة الرئيسية (Home Screen)
- أزرار التنقل
- تحميل التطبيق الكامل
- عمل المزودات (Providers)

**مثال:**
```dart
testWidgets('يجب أن تظهر شاشة البداية بشكل صحيح', (WidgetTester tester) async {
  await tester.pumpWidget(const MaterialApp(home: SplashScreen()));
  expect(find.text('أسامة ماركت'), findsOneWidget);
});
```

### **⚙️ اختبارات الوحدة (Unit Tests)**
**الهدف:** اختبار الوظائف والمنطق التجاري

**ما يتم اختباره:**
- النماذج (Models): Product, Customer, Sale
- المنسقات (Formatters): العملة، التاريخ، الأرقام
- المدققات (Validators): البريد، الهاتف، الأسعار
- نظام الألوان (AppColors)
- الحسابات التجارية

**مثال:**
```dart
test('يجب أن ينسق العملة بشكل صحيح', () {
  expect(Formatters.formatCurrency(100.0), equals('100.00 ر.س'));
});
```

### **🔄 اختبارات المزودات (Provider Tests)**
**الهدف:** اختبار إدارة الحالة والبيانات

**ما يتم اختباره:**
- ProductProvider: إدارة المنتجات
- CustomerProvider: إدارة العملاء
- SaleProvider: إدارة المبيعات
- BackupProvider: النسخ الاحتياطي

**مثال:**
```dart
test('يجب أن يضيف منتج جديد بشكل صحيح', () async {
  await productProvider.addProduct(testProduct);
  expect(productProvider.products.length, equals(1));
});
```

### **🛠️ اختبارات الخدمات (Service Tests)**
**الهدف:** اختبار طبقة البيانات وقاعدة البيانات

**ما يتم اختباره:**
- DatabaseService: الاتصال وإنشاء الجداول
- ProductService: عمليات المنتجات
- CustomerService: عمليات العملاء
- SaleService: عمليات المبيعات
- اختبارات التكامل

**مثال:**
```dart
test('يجب أن تضيف منتج جديد بنجاح', () async {
  final int productId = await productService.addProduct(testProduct);
  expect(productId, greaterThan(0));
});
```

## 🎯 **اختبارات متقدمة**

### **📈 اختبارات الأداء:**
- أوقات الاستجابة
- استخدام الذاكرة
- العمليات المتزامنة

### **🔒 اختبارات الأمان:**
- التعامل مع البيانات الفارغة
- معالجة الأخطاء
- حماية من SQL Injection

### **🌐 اختبارات التوافق:**
- أنواع البيانات المختلفة
- التنسيقات المختلفة
- المنصات المختلفة (Android, iOS, Web)

### **🚀 اختبارات الإنتاجية:**
- الكميات الكبيرة من البيانات
- العمليات الحسابية المعقدة
- عمليات النص بكفاءة

## 📋 **قائمة مراجعة الاختبارات**

### **قبل إضافة ميزة جديدة:**
- [ ] كتابة اختبارات الوحدة للوظائف الجديدة
- [ ] كتابة اختبارات الواجهة للشاشات الجديدة
- [ ] اختبار التكامل مع الميزات الموجودة
- [ ] اختبار معالجة الأخطاء

### **قبل النشر:**
- [ ] تشغيل جميع الاختبارات
- [ ] التأكد من نجاح جميع الاختبارات
- [ ] مراجعة تقرير التغطية
- [ ] اختبار على منصات مختلفة

## 🔧 **إضافة اختبارات جديدة**

### **1. اختبار وحدة جديد:**
```dart
test('وصف الاختبار', () {
  // ترتيب البيانات
  final testData = createTestData();
  
  // تنفيذ العملية
  final result = performOperation(testData);
  
  // التحقق من النتيجة
  expect(result, equals(expectedValue));
});
```

### **2. اختبار واجهة جديد:**
```dart
testWidgets('وصف اختبار الواجهة', (WidgetTester tester) async {
  // بناء الواجهة
  await tester.pumpWidget(MyWidget());
  
  // التفاعل مع العناصر
  await tester.tap(find.byType(ElevatedButton));
  await tester.pumpAndSettle();
  
  // التحقق من النتيجة
  expect(find.text('النتيجة المتوقعة'), findsOneWidget);
});
```

### **3. اختبار مزود جديد:**
```dart
test('اختبار مزود جديد', () async {
  // إعداد المزود
  final provider = MyProvider();
  
  // تنفيذ العملية
  await provider.performAction();
  
  // التحقق من الحالة
  expect(provider.state, equals(expectedState));
});
```

## 📊 **تفسير نتائج الاختبارات**

### **✅ نجح الاختبار:**
```
✓ يجب أن ينسق العملة بشكل صحيح
```

### **❌ فشل الاختبار:**
```
✗ يجب أن ينسق العملة بشكل صحيح
  Expected: '100.00 ر.س'
  Actual: '100.0 ر.س'
```

### **⚠️ تحذير:**
```
! اختبار يستغرق وقتاً طويلاً
```

## 🎯 **أفضل الممارسات**

### **1. كتابة الاختبارات:**
- اكتب اختبارات واضحة ومفهومة
- استخدم أسماء وصفية للاختبارات
- اختبر حالة واحدة في كل اختبار
- استخدم البيانات الوهمية للاختبار

### **2. تنظيم الاختبارات:**
- جمع الاختبارات المترابطة في مجموعات
- استخدم `setUp()` و `tearDown()` عند الحاجة
- فصل اختبارات الوحدة عن اختبارات التكامل

### **3. صيانة الاختبارات:**
- حدث الاختبارات عند تغيير الكود
- احذف الاختبارات غير المستخدمة
- راجع الاختبارات دورياً

## 🚨 **استكشاف الأخطاء**

### **مشكلة: فشل الاختبارات**
1. تحقق من رسالة الخطأ
2. راجع البيانات المستخدمة
3. تأكد من إعداد البيئة
4. شغل الاختبار منفرداً

### **مشكلة: بطء الاختبارات**
1. قلل من البيانات المستخدمة
2. استخدم البيانات الوهمية
3. تجنب العمليات الثقيلة
4. استخدم `pumpAndSettle()` بحذر

### **مشكلة: اختبارات غير مستقرة**
1. تجنب الاعتماد على التوقيت
2. استخدم `await` مع العمليات غير المتزامنة
3. تنظيف البيانات بعد كل اختبار

---

**تذكر:** الاختبارات استثمار في جودة الكود وموثوقية التطبيق! 🧪✨
