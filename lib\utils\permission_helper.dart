/// مساعد الصلاحيات مع واجهة المستخدم لتطبيق أسامة ماركت
/// 
/// يوفر طرق سهلة لطلب الصلاحيات مع عرض رسائل واضحة للمستخدم
/// ويتعامل مع جميع الحالات المختلفة للصلاحيات
/// 
/// الاستخدام:
/// ```dart
/// final bool granted = await PermissionHelper.requestStorageWithDialog(context);
/// if (granted) {
///   // تنفيذ العملية
/// }
/// ```

import 'package:flutter/material.dart';
import '../services/permission_service.dart';
import '../utils/snackbar_helper.dart';
import '../widgets/enhanced_confirmation_dialog.dart';

/// كلاس مساعد الصلاحيات مع واجهة المستخدم
/// 
/// يوفر طرق لطلب الصلاحيات مع عرض حوارات وتوضيحات للمستخدم
class PermissionHelper {
  
  /// طلب صلاحية التخزين مع حوار توضيحي
  /// 
  /// يعرض حوار يوضح سبب الحاجة للصلاحية قبل طلبها
  /// [context] - سياق الواجهة لعرض الحوارات
  /// Returns: true إذا تم منح الصلاحية
  static Future<bool> requestStorageWithDialog(BuildContext context) async {
    // فحص الصلاحية أولاً
    final bool hasPermission = await PermissionService.checkStoragePermission();
    if (hasPermission) {
      return true;
    }

    // عرض حوار توضيحي
    final bool? shouldRequest = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('صلاحية التخزين'),
        content: const Text(
          'يحتاج التطبيق لصلاحية التخزين لحفظ:\n'
          '• النسخ الاحتياطية\n'
          '• تقارير Excel\n'
          '• الفواتير المطبوعة\n'
          '• ملفات الاستيراد والتصدير\n\n'
          'هل تريد منح هذه الصلاحية؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('نعم'),
          ),
        ],
      ),
    );

    if (shouldRequest != true) {
      SnackBarHelper.showWarning(context, 'تم إلغاء طلب صلاحية التخزين');
      return false;
    }

    // طلب الصلاحية
    final bool granted = await PermissionService.requestStoragePermission();
    
    if (granted) {
      SnackBarHelper.showSuccess(context, 'تم منح صلاحية التخزين بنجاح');
    } else {
      SnackBarHelper.showError(context, 'تم رفض صلاحية التخزين');
      _showPermissionDeniedDialog(context, 'التخزين');
    }

    return granted;
  }

  /// طلب صلاحية الكاميرا مع حوار توضيحي
  static Future<bool> requestCameraWithDialog(BuildContext context) async {
    final bool hasPermission = await PermissionService.checkCameraPermission();
    if (hasPermission) {
      return true;
    }

    final bool? shouldRequest = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('صلاحية الكاميرا'),
        content: const Text(
          'يحتاج التطبيق لصلاحية الكاميرا لـ:\n'
          '• مسح الباركود\n'
          '• التقاط صور المنتجات\n'
          '• مسح QR Code للمدفوعات\n\n'
          'هل تريد منح هذه الصلاحية؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('نعم'),
          ),
        ],
      ),
    );

    if (shouldRequest != true) {
      SnackBarHelper.showWarning(context, 'تم إلغاء طلب صلاحية الكاميرا');
      return false;
    }

    final bool granted = await PermissionService.requestCameraPermission();
    
    if (granted) {
      SnackBarHelper.showSuccess(context, 'تم منح صلاحية الكاميرا بنجاح');
    } else {
      SnackBarHelper.showError(context, 'تم رفض صلاحية الكاميرا');
      _showPermissionDeniedDialog(context, 'الكاميرا');
    }

    return granted;
  }

  /// طلب صلاحية الموقع مع حوار توضيحي
  static Future<bool> requestLocationWithDialog(BuildContext context) async {
    final bool hasPermission = await PermissionService.checkLocationPermission();
    if (hasPermission) {
      return true;
    }

    final bool? shouldRequest = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('صلاحية الموقع'),
        content: const Text(
          'يحتاج التطبيق لصلاحية الموقع لـ:\n'
          '• تحديد موقع المتجر\n'
          '• حساب المسافة للعملاء\n'
          '• خدمات التوصيل\n'
          '• التقارير الجغرافية\n\n'
          'هل تريد منح هذه الصلاحية؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('نعم'),
          ),
        ],
      ),
    );

    if (shouldRequest != true) {
      SnackBarHelper.showWarning(context, 'تم إلغاء طلب صلاحية الموقع');
      return false;
    }

    final bool granted = await PermissionService.requestLocationPermission();
    
    if (granted) {
      SnackBarHelper.showSuccess(context, 'تم منح صلاحية الموقع بنجاح');
    } else {
      SnackBarHelper.showError(context, 'تم رفض صلاحية الموقع');
      _showPermissionDeniedDialog(context, 'الموقع');
    }

    return granted;
  }

  /// طلب صلاحية جهات الاتصال مع حوار توضيحي
  static Future<bool> requestContactsWithDialog(BuildContext context) async {
    final bool hasPermission = await PermissionService.checkContactsPermission();
    if (hasPermission) {
      return true;
    }

    final bool? shouldRequest = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('صلاحية جهات الاتصال'),
        content: const Text(
          'يحتاج التطبيق لصلاحية جهات الاتصال لـ:\n'
          '• استيراد بيانات العملاء\n'
          '• ربط العملاء بجهات الاتصال\n'
          '• إرسال الفواتير عبر الواتساب\n\n'
          'هل تريد منح هذه الصلاحية؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('نعم'),
          ),
        ],
      ),
    );

    if (shouldRequest != true) {
      SnackBarHelper.showWarning(context, 'تم إلغاء طلب صلاحية جهات الاتصال');
      return false;
    }

    final bool granted = await PermissionService.requestContactsPermission();
    
    if (granted) {
      SnackBarHelper.showSuccess(context, 'تم منح صلاحية جهات الاتصال بنجاح');
    } else {
      SnackBarHelper.showError(context, 'تم رفض صلاحية جهات الاتصال');
      _showPermissionDeniedDialog(context, 'جهات الاتصال');
    }

    return granted;
  }

  /// طلب صلاحية الإشعارات مع حوار توضيحي
  static Future<bool> requestNotificationWithDialog(BuildContext context) async {
    final bool hasPermission = await PermissionService.checkNotificationPermission();
    if (hasPermission) {
      return true;
    }

    final bool? shouldRequest = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('صلاحية الإشعارات'),
        content: const Text(
          'يحتاج التطبيق لصلاحية الإشعارات لـ:\n'
          '• تنبيهات انتهاء المخزون\n'
          '• تذكير بالمواعيد المهمة\n'
          '• إشعارات النسخ الاحتياطي\n'
          '• تنبيهات الديون المستحقة\n\n'
          'هل تريد منح هذه الصلاحية؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('نعم'),
          ),
        ],
      ),
    );

    if (shouldRequest != true) {
      SnackBarHelper.showWarning(context, 'تم إلغاء طلب صلاحية الإشعارات');
      return false;
    }

    final bool granted = await PermissionService.requestNotificationPermission();
    
    if (granted) {
      SnackBarHelper.showSuccess(context, 'تم منح صلاحية الإشعارات بنجاح');
    } else {
      SnackBarHelper.showError(context, 'تم رفض صلاحية الإشعارات');
    }

    return granted;
  }

  /// طلب جميع الصلاحيات الأساسية مع حوار واحد
  static Future<Map<String, bool>> requestEssentialPermissionsWithDialog(
      BuildContext context) async {
    final bool? shouldRequest = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('الصلاحيات المطلوبة'),
        content: const Text(
          'يحتاج التطبيق للصلاحيات التالية للعمل بشكل صحيح:\n\n'
          '📁 التخزين - لحفظ البيانات والملفات\n'
          '🔔 الإشعارات - للتنبيهات المهمة\n\n'
          'هل تريد منح هذه الصلاحيات؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('نعم، منح الصلاحيات'),
          ),
        ],
      ),
    );

    if (shouldRequest != true) {
      SnackBarHelper.showWarning(context, 'تم إلغاء طلب الصلاحيات');
      return <String, bool>{};
    }

    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 20),
            Text('جاري طلب الصلاحيات...'),
          ],
        ),
      ),
    );

    final Map<String, bool> results = 
        await PermissionService.requestEssentialPermissions();

    // إغلاق مؤشر التحميل
    Navigator.of(context).pop();

    // عرض النتائج
    final int grantedCount = results.values.where((granted) => granted).length;
    final int totalCount = results.length;

    if (grantedCount == totalCount) {
      SnackBarHelper.showSuccess(
          context, 'تم منح جميع الصلاحيات المطلوبة');
    } else if (grantedCount > 0) {
      SnackBarHelper.showWarning(
          context, 'تم منح $grantedCount من $totalCount صلاحيات');
    } else {
      SnackBarHelper.showError(context, 'تم رفض جميع الصلاحيات');
    }

    return results;
  }

  /// عرض حوار رفض الصلاحية مع خيار فتح الإعدادات
  static Future<void> _showPermissionDeniedDialog(
      BuildContext context, String permissionName) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('تم رفض صلاحية $permissionName'),
        content: Text(
          'لاستخدام هذه الميزة، يجب منح صلاحية $permissionName.\n\n'
          'يمكنك منح الصلاحية من إعدادات التطبيق.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await PermissionService.openAppSettings();
            },
            child: const Text('فتح الإعدادات'),
          ),
        ],
      ),
    );
  }

  /// فحص الصلاحيات وعرض تقرير للمستخدم
  static Future<void> showPermissionsStatus(BuildContext context) async {
    final Map<String, bool> permissions = 
        await PermissionService.checkAllPermissions();

    final List<String> granted = [];
    final List<String> denied = [];

    permissions.forEach((String name, bool isGranted) {
      if (isGranted) {
        granted.add(_getPermissionDisplayName(name));
      } else {
        denied.add(_getPermissionDisplayName(name));
      }
    });

    await showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('حالة الصلاحيات'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (granted.isNotEmpty) ...[
                const Text('✅ الصلاحيات الممنوحة:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                ...granted.map((name) => Text('• $name')),
                const SizedBox(height: 10),
              ],
              if (denied.isNotEmpty) ...[
                const Text('❌ الصلاحيات المرفوضة:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                ...denied.map((name) => Text('• $name')),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          if (denied.isNotEmpty)
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await PermissionService.openAppSettings();
              },
              child: const Text('فتح الإعدادات'),
            ),
        ],
      ),
    );
  }

  /// تحويل اسم الصلاحية الإنجليزي للعربي
  static String _getPermissionDisplayName(String permissionName) {
    switch (permissionName) {
      case 'storage':
        return 'التخزين';
      case 'camera':
        return 'الكاميرا';
      case 'location':
        return 'الموقع';
      case 'contacts':
        return 'جهات الاتصال';
      case 'notification':
        return 'الإشعارات';
      default:
        return permissionName;
    }
  }
}
