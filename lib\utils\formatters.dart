import 'package:intl/intl.dart';
import '../config/app_constants.dart';

/// فئة لتنسيق البيانات للعرض
class Formatters {
  /// تنسيق العملة
  static String formatCurrency(double amount) {
    final NumberFormat formatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: AppConstants.defaultCurrency,
      decimalDigits: AppConstants.decimalPlaces,
    );
    return formatter.format(amount);
  }

  /// تنسيق الرقم مع فواصل الآلاف
  static String formatNumber(double number) {
    final NumberFormat formatter = NumberFormat('#,##0.##', 'ar_SA');
    return formatter.format(number);
  }

  /// تنسيق التاريخ للعرض
  static String formatDate(DateTime date) {
    final DateFormat formatter =
        DateFormat(AppConstants.displayDateFormat, 'ar_SA');
    return formatter.format(date);
  }

  /// تنسيق التاريخ والوقت للعرض
  static String formatDateTime(DateTime dateTime) {
    final DateFormat formatter =
        DateFormat(AppConstants.displayDateTimeFormat, 'ar_SA');
    return formatter.format(dateTime);
  }

  /// تنسيق الوقت للعرض
  static String formatTime(DateTime time) {
    final DateFormat formatter = DateFormat(AppConstants.timeFormat, 'ar_SA');
    return formatter.format(time);
  }

  /// تنسيق التاريخ النسبي (منذ كم من الوقت)
  static String formatRelativeTime(DateTime dateTime) {
    final DateTime now = DateTime.now();
    final Duration difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      final int years = (difference.inDays / 365).floor();
      return 'منذ $years ${years == 1 ? 'سنة' : 'سنوات'}';
    } else if (difference.inDays > 30) {
      final int months = (difference.inDays / 30).floor();
      return 'منذ $months ${months == 1 ? 'شهر' : 'أشهر'}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  /// تنسيق حجم الملف
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes بايت';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
    }
  }

  /// تنسيق النسبة المئوية
  static String formatPercentage(double value, {int decimalPlaces = 1}) {
    return '${value.toStringAsFixed(decimalPlaces)}%';
  }

  /// تنسيق رقم الهاتف
  static String formatPhoneNumber(String phone) {
    // إزالة جميع الرموز والمسافات
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');

    if (cleaned.startsWith('+966')) {
      final String number = cleaned.substring(4);
      if (number.length == 9) {
        return '+966 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}';
      }
    }

    return phone;
  }

  /// تنسيق رقم الفاتورة
  static String formatInvoiceNumber(String prefix, int number) {
    return '$prefix-${number.toString().padLeft(6, '0')}';
  }

  /// تنسيق الكمية مع الوحدة
  static String formatQuantityWithUnit(double quantity, String unit) {
    final String formattedQuantity = quantity % 1 == 0
        ? quantity.toInt().toString()
        : quantity.toStringAsFixed(2);
    return '$formattedQuantity $unit';
  }

  /// تنسيق حالة المخزون
  static String formatStockStatus(int quantity, int minLevel) {
    if (quantity <= 0) {
      return 'نفد المخزون';
    } else if (quantity <= minLevel) {
      return 'مخزون منخفض';
    } else if (quantity <= minLevel * 2) {
      return 'مخزون متوسط';
    } else {
      return 'مخزون جيد';
    }
  }

  /// تنسيق حالة الفاتورة
  static String formatInvoiceStatus(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'مسودة';
      case 'pending':
        return 'في الانتظار';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  /// تنسيق طريقة الدفع
  static String formatPaymentMethod(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'نقداً';
      case 'credit_card':
        return 'بطاقة ائتمان';
      case 'bank_transfer':
        return 'تحويل بنكي';
      case 'check':
        return 'شيك';
      case 'deferred':
        return 'آجل';
      default:
        return method;
    }
  }

  /// تنسيق نوع المعاملة
  static String formatTransactionType(String type) {
    switch (type.toLowerCase()) {
      case 'sale':
        return 'بيع';
      case 'purchase':
        return 'شراء';
      case 'expense':
        return 'مصروف';
      case 'payment':
        return 'دفع';
      case 'receipt':
        return 'استلام';
      default:
        return type;
    }
  }

  /// تنسيق النص للعرض (اختصار النص الطويل)
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return '${text.substring(0, maxLength)}...';
  }

  /// تنسيق الاسم (الحرف الأول كبير)
  static String formatName(String name) {
    if (name.isEmpty) return name;

    return name.split(' ').map((String word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  /// تنسيق رقم الهوية/السجل التجاري
  static String formatIdNumber(String id) {
    if (id.length == 10) {
      // رقم الهوية السعودي
      return '${id.substring(0, 1)}-${id.substring(1, 5)}-${id.substring(5, 9)}-${id.substring(9)}';
    } else if (id.length == 15) {
      // رقم الإقامة
      return '${id.substring(0, 2)}-${id.substring(2, 6)}-${id.substring(6, 10)}-${id.substring(10)}';
    }
    return id;
  }

  /// تنسيق الرقم التسلسلي
  static String formatSerialNumber(int number, {int digits = 6}) {
    return number.toString().padLeft(digits, '0');
  }

  /// تنسيق المدة الزمنية
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} ${duration.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ${duration.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} ${duration.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return '${duration.inSeconds} ${duration.inSeconds == 1 ? 'ثانية' : 'ثوان'}';
    }
  }

  /// تنسيق العنوان (إزالة المسافات الزائدة)
  static String formatAddress(String address) {
    return address.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// تنسيق البريد الإلكتروني (تحويل إلى أحرف صغيرة)
  static String formatEmail(String email) {
    return email.trim().toLowerCase();
  }

  /// تنسيق الرقم العشري
  static String formatDecimal(double value, {int decimalPlaces = 2}) {
    return value.toStringAsFixed(decimalPlaces);
  }

  /// تنسيق الرقم الصحيح مع فواصل الآلاف
  static String formatInteger(int value) {
    final NumberFormat formatter = NumberFormat('#,##0', 'ar_SA');
    return formatter.format(value);
  }
}
