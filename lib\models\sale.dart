/// Model class representing a sale transaction
class Sale {
  /// Unique identifier for the sale
  int? id;

  /// Customer ID for this sale
  int? customerId;

  /// Date of the sale
  String? date;

  /// Date of the sale as DateTime object
  DateTime? saleDateObj;

  /// Total amount of the sale
  double? total;

  /// Additional notes for the sale
  String? notes;

  /// Status of the sale (completed, pending, cancelled)
  String? status;

  /// Invoice number for the sale
  String? invoiceNumber;

  /// Customer name for display purposes
  String? customerName;

  /// Payment method (cash, card, etc.)
  String? paymentMethod;

  /// حقول جديدة لنظام المخزن والبقالة
  /// إجمالي مبيعات الجملة في هذه الفاتورة
  double? totalWholesaleAmount;

  /// إجمالي مبيعات التجزئة في هذه الفاتورة
  double? totalRetailAmount;

  /// المبلغ المتبقي كدين من بضاعة التجزئة في هذه الفاتورة
  double? remainingRetailAmount;

  /// ملاحظات على بنود التجزئة
  String? notesForRetailItems;

  /// المبلغ المدفوع
  double? paidAmount;

  /// المبلغ المتبقي
  double? remainingAmount;

  /// نوع البيع (retail/wholesale)
  String? saleType;

  /// تاريخ الإنشاء
  DateTime? createdAt;

  /// تاريخ التحديث
  DateTime? updatedAt;

  /// Getter for saleDate (alias for date)
  String? get saleDate => date;

  /// Getter for totalAmount (alias for total)
  double? get totalAmount => total;

  /// Constructor for creating a Sale instance
  Sale({
    this.id,
    this.customerId,
    this.date,
    this.total,
    this.notes,
    this.status,
    this.invoiceNumber,
    this.customerName,
    this.paymentMethod,
    this.totalWholesaleAmount,
    this.totalRetailAmount,
    this.remainingRetailAmount,
    this.notesForRetailItems,
    this.paidAmount,
    this.remainingAmount,
    this.saleType,
    this.createdAt,
    this.updatedAt,
    double? totalAmount, // Accept totalAmount parameter
  }) {
    if (totalAmount != null) {
      total = totalAmount;
    }
  }

  /// Converts the Sale instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'customerId': customerId,
      'date': date,
      'total': total,
      'notes': notes,
      'status': status,
      'invoiceNumber': invoiceNumber,
      'customerName': customerName,
      'paymentMethod': paymentMethod,
      'totalWholesaleAmount': totalWholesaleAmount,
      'totalRetailAmount': totalRetailAmount,
      'remainingRetailAmount': remainingRetailAmount,
      'notesForRetailItems': notesForRetailItems,
      'paidAmount': paidAmount,
      'remainingAmount': remainingAmount,
      'saleType': saleType,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Creates a Sale instance from a Map (typically from database)
  factory Sale.fromMap(Map<String, dynamic> map) {
    return Sale(
      id: map['id'] as int?,
      customerId: map['customerId'] as int?,
      date: map['date'] as String?,
      total: map['total']?.toDouble(),
      notes: map['notes'] as String?,
      status: map['status'] as String?,
      invoiceNumber: map['invoiceNumber'] as String?,
      customerName: map['customerName'] as String?,
      paymentMethod: map['paymentMethod'] as String?,
      totalWholesaleAmount: map['totalWholesaleAmount']?.toDouble(),
      totalRetailAmount: map['totalRetailAmount']?.toDouble(),
      remainingRetailAmount: map['remainingRetailAmount']?.toDouble(),
      notesForRetailItems: map['notesForRetailItems'] as String?,
      paidAmount: map['paidAmount']?.toDouble(),
      remainingAmount: map['remainingAmount']?.toDouble(),
      saleType: map['saleType'] as String?,
      createdAt: map['createdAt'] != null
          ? DateTime.tryParse(map['createdAt'] as String)
          : null,
      updatedAt: map['updatedAt'] != null
          ? DateTime.tryParse(map['updatedAt'] as String)
          : null,
    );
  }

  @override
  String toString() {
    return 'Sale{id: $id, customerId: $customerId, date: $date, '
        'total: $total, notes: $notes, status: $status}';
  }
}
