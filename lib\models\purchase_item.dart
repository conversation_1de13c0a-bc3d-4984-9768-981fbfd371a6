/// Model class for PurchaseItem (عنصر فاتورة التوريد)
class PurchaseItem {
  /// Unique identifier for the purchase item
  int? id;

  /// ID of the purchase this item belongs to
  int? purchaseId;

  /// ID of the product
  int? productId;

  /// Quantity of the product purchased
  double? quantity;

  /// Price per unit for this purchase item
  double? price;

  /// Product name for display purposes
  String? productName;

  /// Unit price (alias for price)
  double? get unitPrice => price;

  /// Total price for this item (quantity * unit price)
  double? get totalPrice => (quantity ?? 0) * (price ?? 0);

  /// Constructor for creating a PurchaseItem instance
  PurchaseItem({
    this.id,
    this.purchaseId,
    this.productId,
    this.quantity,
    this.price,
    this.productName,
    double? unitPrice,
    double? totalPrice, // This will be calculated automatically
  }) {
    if (unitPrice != null) {
      price = unitPrice;
    }
  }

  /// Converts the PurchaseItem instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'purchaseId': purchaseId,
      'productId': productId,
      'quantity': quantity,
      'price': price,
      'productName': productName,
    };
  }

  /// Creates a PurchaseItem instance from a Map (typically from database)
  factory PurchaseItem.fromMap(Map<String, dynamic> map) {
    return PurchaseItem(
      id: map['id'] as int?,
      purchaseId: map['purchaseId'] as int?,
      productId: map['productId'] as int?,
      quantity: map['quantity']?.toDouble(),
      price: map['price']?.toDouble(),
      productName: map['productName'] as String?,
    );
  }

  @override
  String toString() {
    return 'PurchaseItem{id: $id, purchaseId: $purchaseId, productId: $productId, '
        'quantity: $quantity, price: $price, productName: $productName}';
  }

  /// Copy with method for creating modified copies
  PurchaseItem copyWith({
    int? id,
    int? purchaseId,
    int? productId,
    String? productName,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
  }) {
    return PurchaseItem(
      id: id ?? this.id,
      purchaseId: purchaseId ?? this.purchaseId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? price,
    );
  }
}
