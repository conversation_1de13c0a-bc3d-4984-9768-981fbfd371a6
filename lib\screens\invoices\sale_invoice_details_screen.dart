import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/sale.dart';
import '../../models/sale_item.dart';
import '../../models/customer.dart';
import '../../providers/sale_provider.dart';
import '../../providers/customer_provider.dart';
// import '../../providers/print_export_provider.dart'; // ملف محذوف
import '../dialogs/confirmation_dialog.dart';
import 'sale_invoice_form_screen.dart';

class SaleInvoiceDetailsScreen extends StatefulWidget {
  final Sale sale;

  const SaleInvoiceDetailsScreen({
    super.key,
    required this.sale,
  });

  @override
  State<SaleInvoiceDetailsScreen> createState() =>
      _SaleInvoiceDetailsScreenState();
}

class _SaleInvoiceDetailsScreenState extends State<SaleInvoiceDetailsScreen> {
  List<SaleItem> _saleItems = <SaleItem>[];
  Customer? _customer;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSaleDetails();
  }

  Future<void> _loadSaleDetails() async {
    try {
      final SaleProvider saleProvider = context.read<SaleProvider>();
      final CustomerProvider customerProvider =
          context.read<CustomerProvider>();

      // تحميل عناصر الفاتورة
      _saleItems = await saleProvider.getSaleItems(widget.sale.id!);

      // تحميل بيانات العميل
      if (widget.sale.customerId != null) {
        _customer = customerProvider.customers
            .where((Customer c) => c.id == widget.sale.customerId)
            .firstOrNull;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في تحميل البيانات: $e')),
        );
      }
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final DateTime date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text('فاتورة بيع رقم ${widget.sale.id}'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: <Widget>[
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          // معلومات الفاتورة الأساسية
                          _buildInvoiceHeader(),

                          const SizedBox(height: 24),

                          // معلومات العميل
                          _buildCustomerInfo(),

                          const SizedBox(height: 24),

                          // المنتجات
                          _buildProductsList(),

                          const SizedBox(height: 24),

                          // المبلغ الإجمالي
                          _buildTotalAmount(),

                          const SizedBox(height: 16),

                          // الملاحظات
                          if (widget.sale.notes != null &&
                              widget.sale.notes!.isNotEmpty)
                            _buildNotes(),
                        ],
                      ),
                    ),
                  ),

                  // شريط الأزرار السفلي
                  _buildActionButtons(),
                ],
              ),
      ),
    );
  }

  Widget _buildInvoiceHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              'معلومات الفاتورة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: <Widget>[
                const Icon(Icons.receipt_long, color: Colors.grey),
                const SizedBox(width: 8),
                Text('رقم الفاتورة: ${widget.sale.id}'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: <Widget>[
                const Icon(Icons.calendar_today, color: Colors.grey),
                const SizedBox(width: 8),
                Text('التاريخ: ${_formatDate(widget.sale.date)}'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: <Widget>[
                Icon(
                  widget.sale.status == 'completed'
                      ? Icons.check_circle
                      : Icons.pending,
                  color: widget.sale.status == 'completed'
                      ? Colors.green
                      : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'الحالة: ${widget.sale.status == 'completed' ? 'مكتملة' : 'معلقة'}',
                  style: TextStyle(
                    color: widget.sale.status == 'completed'
                        ? Colors.green
                        : Colors.orange,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              'معلومات العميل',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 12),
            if (_customer != null) ...<Widget>[
              Row(
                children: <Widget>[
                  const Icon(Icons.person, color: Colors.grey),
                  const SizedBox(width: 8),
                  Text('الاسم: ${_customer!.name}'),
                ],
              ),
              if (_customer!.phone != null &&
                  _customer!.phone!.isNotEmpty) ...<Widget>[
                const SizedBox(height: 8),
                Row(
                  children: <Widget>[
                    const Icon(Icons.phone, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text('الهاتف: ${_customer!.phone}'),
                  ],
                ),
              ],
              if (_customer!.email != null &&
                  _customer!.email!.isNotEmpty) ...<Widget>[
                const SizedBox(height: 8),
                Row(
                  children: <Widget>[
                    const Icon(Icons.email, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text('البريد الإلكتروني: ${_customer!.email}'),
                  ],
                ),
              ],
            ] else
              const Text(
                'لم يتم العثور على بيانات العميل',
                style: TextStyle(color: Colors.grey),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsList() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              'المنتجات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 12),
            if (_saleItems.isEmpty)
              const Text(
                'لا توجد منتجات في هذه الفاتورة',
                style: TextStyle(color: Colors.grey),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _saleItems.length,
                separatorBuilder: (BuildContext context, int index) =>
                    const Divider(),
                itemBuilder: (BuildContext context, int index) {
                  final SaleItem item = _saleItems[index];
                  return _buildProductItem(item);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductItem(SaleItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  item.productName ?? 'منتج غير محدد',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'الكمية: ${item.quantity?.toStringAsFixed(0) ?? '0'}',
                  style: const TextStyle(color: Colors.grey),
                ),
                Text(
                  'السعر: ${item.unitPrice?.toStringAsFixed(2) ?? '0.00'} ر.س',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
          Text(
            '${item.totalPrice?.toStringAsFixed(2) ?? '0.00'} ر.س',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalAmount() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Text(
        'المبلغ الإجمالي: ${widget.sale.totalAmount?.toStringAsFixed(2) ?? '0.00'} ر.س',
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildNotes() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              'الملاحظات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.sale.notes!,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: <Widget>[
          // الصف الأول من الأزرار
          Row(
            children: <Widget>[
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _editInvoice,
                  icon: const Icon(Icons.edit),
                  label: const Text('تعديل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _saveAsPDF,
                  icon: const Icon(Icons.picture_as_pdf),
                  label: const Text('PDF'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // الصف الثاني من الأزرار
          Row(
            children: <Widget>[
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _printInvoice,
                  icon: const Icon(Icons.print),
                  label: const Text('طباعة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _shareInvoice,
                  icon: const Icon(Icons.share),
                  label: const Text('مشاركة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          if (widget.sale.status != 'completed') ...<Widget>[
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _markAsCompleted,
                icon: const Icon(Icons.check_circle),
                label: const Text('تعليم كمدفوع'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _editInvoice() async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => const ConfirmationDialog(
        title: 'تأكيد التعديل',
        content:
            'هل أنت متأكد من رغبتك في تعديل هذه الفاتورة؟ أي تعديل سيؤثر على المخزون والمبالغ.',
      ),
    );

    if (confirmed == true && mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (BuildContext context) =>
              CreateSaleInvoiceScreen(existingSale: widget.sale),
        ),
      );
    }
  }

  Future<void> _saveAsPDF() async {
    try {
      // TODO: إضافة PrintExportProvider لاحقاً
      // final printProvider = context.read<PrintExportProvider>();
      // await printProvider.generateSaleInvoicePDF(widget.sale, _saleItems, _customer);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ الفاتورة كملف PDF بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في حفظ PDF: $e')),
        );
      }
    }
  }

  Future<void> _printInvoice() async {
    try {
      // TODO: إضافة PrintExportProvider لاحقاً
      // final printProvider = context.read<PrintExportProvider>();
      // await printProvider.printSaleInvoice(widget.sale, _saleItems, _customer);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في الطباعة: $e')),
        );
      }
    }
  }

  Future<void> _shareInvoice() async {
    try {
      // TODO: إضافة PrintExportProvider لاحقاً
      // final printProvider = context.read<PrintExportProvider>();
      // await printProvider.shareSaleInvoice(widget.sale, _saleItems, _customer);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في المشاركة: $e')),
        );
      }
    }
  }

  Future<void> _markAsCompleted() async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => const ConfirmationDialog(
        title: 'تأكيد الدفع',
        content: 'هل أنت متأكد من تعليم هذه الفاتورة كمدفوعة؟',
      ),
    );

    if (confirmed == true) {
      try {
        final SaleProvider saleProvider = context.read<SaleProvider>();
        final Sale updatedSale = Sale(
          id: widget.sale.id,
          customerId: widget.sale.customerId,
          date: widget.sale.date,
          totalAmount: widget.sale.totalAmount,
          notes: widget.sale.notes,
          status: 'completed',
        );

        await saleProvider.updateSaleStatus(updatedSale);

        if (mounted) {
          setState(() {
            // تحديث الحالة محلياً
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تعليم الفاتورة كمدفوعة بنجاح')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: $e')),
          );
        }
      }
    }
  }
}
