import 'package:flutter/material.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';

/// شاشة الإعدادات
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: const Text('الإعدادات'),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
        ),
        body: ListView(
          padding: const EdgeInsets.all(16),
          children: <Widget>[
            _buildSection(
              'عام',
              <Widget>[
                _buildSettingTile(
                  'اللغة',
                  'العربية',
                  Icons.language,
                  () {},
                ),
                _buildSettingTile(
                  'المظهر',
                  'فاتح',
                  Icons.brightness_6,
                  () {},
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildSection(
              'الشركة',
              <Widget>[
                _buildSettingTile(
                  'اسم الشركة',
                  'شركة المخزون',
                  Icons.business,
                  () {},
                ),
                _buildSettingTile(
                  'العنوان',
                  'الرياض، السعودية',
                  Icons.location_on,
                  () {},
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildSection(
              'النسخ الاحتياطي',
              <Widget>[
                _buildSettingTile(
                  'نسخ احتياطي تلقائي',
                  'مفعل',
                  Icons.backup,
                  () {},
                ),
                _buildSettingTile(
                  'استعادة البيانات',
                  '',
                  Icons.restore,
                  () {},
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppColors.primary),
      title: Text(title),
      subtitle: subtitle.isNotEmpty ? Text(subtitle) : null,
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }
}
