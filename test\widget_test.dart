/// اختبارات الواجهة (Widget Tests) لتطبيق أسامة ماركت
///
/// تتضمن اختبارات للشاشات الأساسية والعناصر المهمة
/// للتأكد من عمل الواجهة بشكل صحيح
library widget_test;

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:inventory_management_app/main.dart';
import 'package:inventory_management_app/screens/splash/splash_screen.dart';
import 'package:inventory_management_app/screens/home/<USER>';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/services/database_service.dart';

void main() {
  group('اختبارات الواجهة الأساسية', () {
    testWidgets('يجب أن تظهر شاشة البداية بشكل صحيح',
        (WidgetTester tester) async {
      // بناء التطبيق
      await tester.pumpWidget(const MaterialApp(
        home: SplashScreen(),
      ));

      // التحقق من وجود عناصر شاشة البداية
      expect(find.text('أسامة ماركت'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('يجب أن تظهر الشاشة الرئيسية بالعناصر الأساسية',
        (WidgetTester tester) async {
      // إعداد قاعدة البيانات للاختبار
      await DatabaseService.instance.database;

      // بناء التطبيق مع Providers
      await tester.pumpWidget(
        MultiProvider(
          providers: <ChangeNotifierProvider<dynamic>>[
            ChangeNotifierProvider<ProductProvider>(
                create: (_) => ProductProvider()),
            ChangeNotifierProvider<CustomerProvider>(
                create: (_) => CustomerProvider()),
          ],
          child: const MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      // انتظار تحميل البيانات
      await tester.pumpAndSettle();

      // التحقق من وجود العناصر الأساسية
      expect(find.text('أسامة ماركت'), findsOneWidget);
      expect(find.text('المبيعات'), findsOneWidget);
      expect(find.text('المشتريات'), findsOneWidget);
      expect(find.text('المنتجات'), findsOneWidget);
      expect(find.text('العملاء'), findsOneWidget);
    });

    testWidgets('يجب أن تعمل أزرار التنقل في الشاشة الرئيسية',
        (WidgetTester tester) async {
      // إعداد قاعدة البيانات للاختبار
      await DatabaseService.instance.database;

      // بناء التطبيق مع Providers
      await tester.pumpWidget(
        MultiProvider(
          providers: <ChangeNotifierProvider<dynamic>>[
            ChangeNotifierProvider<ProductProvider>(
                create: (_) => ProductProvider()),
            ChangeNotifierProvider<CustomerProvider>(
                create: (_) => CustomerProvider()),
          ],
          child: const MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      // انتظار تحميل البيانات
      await tester.pumpAndSettle();

      // البحث عن زر المبيعات والنقر عليه
      final Finder salesButton = find.text('المبيعات');
      expect(salesButton, findsOneWidget);

      await tester.tap(salesButton);
      await tester.pumpAndSettle();

      // التحقق من أن التنقل يعمل (لا توجد أخطاء)
      expect(tester.takeException(), isNull);
    });
  });

  group('اختبارات التطبيق الكامل', () {
    testWidgets('يجب أن يتم تحميل التطبيق بدون أخطاء',
        (WidgetTester tester) async {
      // بناء التطبيق الكامل
      await tester.pumpWidget(const MyApp());

      // انتظار تحميل جميع العناصر
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // التحقق من عدم وجود أخطاء
      expect(tester.takeException(), isNull);
    });

    testWidgets('يجب أن تعمل المزودات (Providers) بشكل صحيح',
        (WidgetTester tester) async {
      // بناء التطبيق الكامل
      await tester.pumpWidget(const MyApp());

      // انتظار تحميل المزودات
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // التحقق من وجود المزودات في السياق
      final BuildContext context = tester.element(find.byType(MyApp));

      expect(() => Provider.of<ProductProvider>(context, listen: false),
          returnsNormally);
      expect(() => Provider.of<CustomerProvider>(context, listen: false),
          returnsNormally);
    });
  });
}
