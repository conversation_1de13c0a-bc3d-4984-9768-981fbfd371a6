import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/product_provider.dart';
import '../../models/product.dart';

class SelectProductForTransferDialog extends StatefulWidget {
  const SelectProductForTransferDialog({super.key});

  @override
  State<SelectProductForTransferDialog> createState() =>
      _SelectProductForTransferDialogState();
}

class _SelectProductForTransferDialogState
    extends State<SelectProductForTransferDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<Product> _filteredProducts = <Product>[];
  Product? _selectedProduct;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  void _loadProducts() {
    final ProductProvider productProvider = context.read<ProductProvider>();
    setState(() {
      _filteredProducts = productProvider.products
          .where((Product product) =>
              (product.warehouseQuantity ?? 0) >
              0) // فقط المنتجات المتوفرة في المخزن
          .toList();
      _isLoading = false;
    });
  }

  void _filterProducts(String query) {
    final ProductProvider productProvider = context.read<ProductProvider>();
    setState(() {
      if (query.isEmpty) {
        _filteredProducts = productProvider.products
            .where((Product product) => (product.warehouseQuantity ?? 0) > 0)
            .toList();
      } else {
        _filteredProducts = productProvider.products
            .where((Product product) =>
                (product.warehouseQuantity ?? 0) > 0 &&
                (product.name.toLowerCase().contains(query.toLowerCase()) ||
                    (product.barcode?.contains(query) ?? false)))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: <Widget>[
              // العنوان
              const Text(
                'اختيار منتج للتحويل',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),

              const SizedBox(height: 16),

              // شريط البحث
              TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  labelText: 'البحث عن منتج',
                  hintText: 'ادخل اسم المنتج أو الباركود',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: _filterProducts,
              ),

              const SizedBox(height: 16),

              // قائمة المنتجات
              Expanded(
                flex: 2,
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredProducts.isEmpty
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Icon(Icons.inventory_2_outlined,
                                    size: 64, color: Colors.grey),
                                SizedBox(height: 16),
                                Text(
                                  'لا توجد منتجات متوفرة في المخزن',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredProducts.length,
                            itemBuilder: (BuildContext context, int index) {
                              final Product product = _filteredProducts[index];
                              return _buildProductItem(product);
                            },
                          ),
              ),

              const SizedBox(height: 16),

              // تفاصيل المنتج المحدد
              if (_selectedProduct != null) ...<Widget>[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        'المنتج المحدد: ${_selectedProduct!.name}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                    'سعر الجملة: ${_selectedProduct!.price.toStringAsFixed(2) ?? '0.00'} ر.س'),
                                Text(
                                    'سعر التجزئة: ${_selectedProduct!.retailPrice?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  'المخزن: ${_selectedProduct!.warehouseQuantity?.toString() ?? '0'}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                                Text(
                                    'البقالة: ${_selectedProduct!.storeQuantity?.toString() ?? '0'}'),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // أزرار الإجراءات
              Row(
                children: <Widget>[
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed:
                          _selectedProduct != null ? _selectProduct : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('اختيار'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductItem(Product product) {
    final bool isSelected = _selectedProduct?.id == product.id;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      color: isSelected ? Colors.blue.shade50 : null,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isSelected ? Colors.blue : Colors.grey.shade300,
          child: Icon(
            Icons.inventory_2,
            color: isSelected ? Colors.white : Colors.grey,
          ),
        ),
        title: Text(
          product.name,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Expanded(
                    child: Text(
                        'جملة: ${product.price.toStringAsFixed(2) ?? '0.00'} ر.س')),
                Expanded(
                    child: Text(
                        'تجزئة: ${product.retailPrice?.toStringAsFixed(2) ?? '0.00'} ر.س')),
              ],
            ),
            Row(
              children: <Widget>[
                Expanded(
                  child: Text(
                    'مخزن: ${product.warehouseQuantity?.toString() ?? '0'}',
                    style: TextStyle(
                      color: (product.warehouseQuantity ?? 0) <= 10
                          ? Colors.orange
                          : Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    'بقالة: ${product.storeQuantity?.toString() ?? '0'}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ),
              ],
            ),
            if (product.barcode != null && product.barcode!.isNotEmpty)
              Text('الباركود: ${product.barcode}'),
          ],
        ),
        trailing: isSelected
            ? const Icon(Icons.check_circle, color: Colors.blue)
            : const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          setState(() {
            _selectedProduct = product;
          });
        },
      ),
    );
  }

  void _selectProduct() {
    if (_selectedProduct != null) {
      Navigator.pop(context, _selectedProduct);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
