import 'package:flutter/material.dart';
import '../models/order.dart';
import '../models/order_item.dart';
import '../models/product.dart';
import '../services/order_service.dart';
import '../services/product_service.dart';

/// Provider for managing order operations
class OrderProvider extends ChangeNotifier {
  final OrderService _orderService = OrderService();
  final ProductService _productService = ProductService();
  // Transaction functionality integrated directly

  List<Order> _orders = <Order>[];
  List<Order> _filteredOrders = <Order>[];
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';
  String _selectedStatus = 'الكل';

  /// Getters
  List<Order> get orders => _orders;
  List<Order> get filteredOrders => _filteredOrders;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get searchQuery => _searchQuery;
  String get selectedStatus => _selectedStatus;

  /// Fetch all orders from the database
  Future<void> fetchOrders() async {
    _setLoading(true);
    _clearError();

    try {
      _orders = await _orderService.getAllOrders();
      _applyFilters();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch orders: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load orders (alias for fetchOrders)
  Future<void> loadOrders() async {
    await fetchOrders();
  }

  /// Initialize the provider
  Future<void> initialize() async {
    await fetchOrders();
  }

  /// Get order items for a specific order
  Future<List<OrderItem>> getOrderItems(int orderId) async {
    try {
      return await _orderService.getOrderItems(orderId);
    } catch (e) {
      _setError('Failed to fetch order items: $e');
      return <OrderItem>[];
    }
  }

  /// Add a new order with items
  Future<Order> addOrder(Order order, List<OrderItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      // Create order with items using transaction service
      await _orderService.insertOrder(order);

      // Reload orders
      await fetchOrders();

      // Return the added order
      final Order addedOrder = _orders.isNotEmpty ? _orders.first : order;
      return addedOrder;
    } catch (e) {
      _setError('Failed to add order: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing order with items
  Future<Order> updateOrder(Order order, List<OrderItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      // Update order with items using transaction service
      // TODO: Implement updateOrderWithItems in TransactionService
      await _orderService.updateOrder(order);

      // Reload orders
      await fetchOrders();

      // Return the updated order
      final Order updatedOrder =
          _orders.where((Order o) => o.id == order.id).firstOrNull ?? order;
      return updatedOrder;
    } catch (e) {
      _setError('Failed to update order: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Update order status only
  Future<void> updateOrderStatus(Order order) async {
    _setLoading(true);
    _clearError();

    try {
      await _orderService.updateOrder(order);
      await fetchOrders();
    } catch (e) {
      _setError('Failed to update order status: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete an order
  Future<void> deleteOrder(int orderId) async {
    _setLoading(true);
    _clearError();

    try {
      await _orderService.deleteOrder(orderId);
      await fetchOrders();
    } catch (e) {
      _setError('Failed to delete order: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Create automatic order for low stock products
  Future<Order?> createAutoLowStockOrder() async {
    _setLoading(true);
    _clearError();

    try {
      // Get all products with low stock
      final List<Product> allProducts = await _productService.getAllProducts();
      final List<Product> lowStockProducts =
          allProducts.where((Product product) {
        final double currentStock = product.quantity ?? 0;
        final int minStock = product.minLevel ?? 10; // Default minimum stock
        return currentStock <= minStock;
      }).toList();

      if (lowStockProducts.isEmpty) {
        _setError('No products with low stock found');
        return null;
      }

      // Create order
      final Order order = Order(
        date: DateTime.now().toIso8601String(),
        status: 'pending',
        type: 'auto_low_stock',
        notes: 'طلبية تلقائية للمنتجات منخفضة المخزون',
      );

      // Create order items
      final List<OrderItem> orderItems =
          lowStockProducts.map((Product product) {
        final double currentStock = product.quantity ?? 0;
        final int minStock = product.minLevel ?? 10;
        final double suggestedQuantity =
            (minStock * 2) - currentStock; // Order double the minimum

        return OrderItem(
          productId: product.id,
          productName: product.name,
          quantity: suggestedQuantity > 0
              ? suggestedQuantity.toDouble()
              : minStock.toDouble(),
          price: product.price ?? 0,
          currentStock: currentStock,
          minStock: minStock.toDouble(),
        );
      }).toList();

      // Calculate total
      final double total = orderItems.fold(
          0.0, (double sum, OrderItem item) => sum + (item.totalPrice ?? 0));
      order.total = total;

      // Save order
      return await addOrder(order, orderItems);
    } catch (e) {
      _setError('Failed to create auto low stock order: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Generate order number
  String generateOrderNumber() {
    final DateTime now = DateTime.now();
    final int count = _orders.length + 1;
    return 'ORD-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${count.toString().padLeft(4, '0')}';
  }

  /// Get total orders amount
  double get totalOrdersAmount {
    return _orders.fold(
        0.0, (double sum, Order order) => sum + (order.total ?? 0));
  }

  /// Get total orders count
  int get totalOrdersCount => _orders.length;

  /// Get pending orders count
  int get pendingOrdersCount {
    return _orders.where((Order order) => order.status == 'pending').length;
  }

  /// Get completed orders count
  int get completedOrdersCount {
    return _orders.where((Order order) => order.status == 'completed').length;
  }

  /// Search orders
  List<Order> searchOrders(String query) {
    if (query.isEmpty) return _orders;

    return _orders.where((Order order) {
      return order.id.toString().contains(query) ||
          (order.notes?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
          (order.type?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  /// Clear all orders
  void clearOrders() {
    _orders.clear();
    _filteredOrders.clear();
    notifyListeners();
  }

  /// Apply search and status filters
  void _applyFilters() {
    _filteredOrders = _orders.where((Order order) {
      // Apply status filter
      bool matchesStatus = _selectedStatus == 'الكل' ||
          _getStatusInArabic(order.status) == _selectedStatus;

      // Apply search query
      bool matchesSearch = _searchQuery.isEmpty ||
          order.id.toString().contains(_searchQuery) ||
          (order.notes?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
              false) ||
          (order.type?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
              false);

      return matchesStatus && matchesSearch;
    }).toList();
  }

  /// Set search query
  void setSearchQuery(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  /// Set status filter
  void setStatusFilter(String status) {
    _selectedStatus = status;
    _applyFilters();
    notifyListeners();
  }

  /// Get order count by status
  int getOrderCountByStatus(String status) {
    if (status == 'الكل') return _orders.length;
    return _orders
        .where((Order order) => _getStatusInArabic(order.status) == status)
        .length;
  }

  /// Convert order to purchase
  Future<void> convertOrderToPurchase(int orderId) async {
    _setLoading(true);
    _clearError();

    try {
      // Find the order
      final Order order = _orders.firstWhere((Order o) => o.id == orderId);

      // Update order status to converted
      order.status = 'converted';
      await _orderService.updateOrder(order);

      // Reload orders
      await fetchOrders();
    } catch (e) {
      _setError('Failed to convert order to purchase: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Helper method to get status in Arabic
  String _getStatusInArabic(String? status) {
    switch (status) {
      case 'pending':
        return 'معلقة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'converted':
        return 'محولة';
      default:
        return 'غير محدد';
    }
  }
}
