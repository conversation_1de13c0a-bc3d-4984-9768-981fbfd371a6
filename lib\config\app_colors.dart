/// ألوان بسيطة وهادئة لتطبيق أسامة ماركت
library;

import 'package:flutter/material.dart';

/// ألوان التطبيق - بسيطة ومتناسقة
class AppColors {
  // الألوان الأساسية - 4 ألوان فقط
  /// اللون الأساسي - أزرق هادئ
  static const Color primary = Color(0xFF1976D2);

  /// خلفية التطبيق - أبيض نقي
  static const Color surface = Color(0xFFFFFFFF);

  /// لون الأخطاء - أحمر هادئ
  static const Color error = Color(0xFFD32F2F);

  /// الفواصل - رمادي فاتح جداً
  static const Color divider = Color(0xFFF5F5F5);

  // ألوان النصوص
  /// النص الأساسي - أسود
  static const Color textPrimary = Color(0xFF212121);

  /// النص الثانوي - رمادي
  static const Color textSecondary = Color(0xFF757575);

  /// النص الثالث - رمادي فاتح
  static const Color textTertiary = Color(0xFF9E9E9E);

  /// النص على الألوان الأساسية - أبيض
  static const Color textOnPrimary = Colors.white;

  // ألوان إضافية مطلوبة
  /// خلفية التطبيق (نفس surface)
  static const Color background = surface;

  /// متغير السطح
  static const Color surfaceVariant = Color(0xFFF5F5F5);

  /// لون النجاح - أخضر
  static const Color success = Color(0xFF4CAF50);

  /// لون النجاح الفاتح
  static const Color successLight = Color(0xFFE8F5E8);

  /// لون الخطأ الفاتح
  static const Color errorLight = Color(0xFFFFEBEE);

  /// لون التحذير - برتقالي
  static const Color warning = Color(0xFFFF9800);

  /// لون التحذير الفاتح
  static const Color warningLight = Color(0xFFFFF3E0);

  /// لون المعلومات - أزرق
  static const Color info = Color(0xFF2196F3);

  /// لون المعلومات الفاتح
  static const Color infoLight = Color(0xFFE3F2FD);

  /// لون الظل
  static const Color shadow = Color(0x1A000000);

  // ألوان إضافية للتوافق مع الكود الموجود
  /// لون ثانوي (نفس primary للبساطة)
  static const Color secondary = primary;

  /// لون التمييز (برتقالي هادئ)
  static const Color accent = Color(0xFFFF9800);

  /// لون التقارير
  static const Color reports = Color(0xFF673AB7);

  // تدرجات بسيطة
  /// تدرج أساسي
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, Color(0xFF1565C0)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ظلال للبطاقات
  /// ظل البطاقات
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 8,
      offset: Offset(0, 2),
    ),
  ];

  // ألوان الوحدات للتوافق مع الاختبارات
  /// لون المبيعات
  static const Color sales = primary;

  /// لون المشتريات
  static const Color purchases = accent;

  /// لون المخزون
  static const Color inventory = success;

  /// لون العملاء
  static const Color customers = info;

  /// لون الموردين
  static const Color suppliers = warning;
}
