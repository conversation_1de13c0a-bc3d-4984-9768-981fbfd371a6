import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/models/sale_item.dart';
import 'package:inventory_management_app/services/sale_service.dart';

/// Provider class for managing sale state and operations
class SaleProvider extends ChangeNotifier {
  List<Sale> _sales = <Sale>[];
  final SaleService _saleService = SaleService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of sales
  List<Sale> get sales => _sales;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Initialize provider and load sales from database
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة SaleProvider...');
      await fetchSales();
      debugPrint('✅ تم تهيئة SaleProvider بنجاح');
    } catch (e, s) {
      debugPrint('❌ خطأ في تهيئة SaleProvider: $e');
      debugPrint('Stack trace: $s');
      _error = 'فشل في تحميل المبيعات: $e';
      notifyListeners();
    }
  }

  /// Fetch all sales from the database
  Future<void> fetchSales() async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _saleService.getAllSales();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch sales: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Alias for fetchSales for compatibility
  Future<void> loadSales() => fetchSales();

  /// Add a new sale (simple version)
  Future<void> addSaleSimple(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.insertSale(sale);
      await fetchSales();
    } catch (e) {
      _setError('Failed to add sale: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing sale (simple version)
  Future<void> updateSaleSimple(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.updateSale(sale);
      await fetchSales();
    } catch (e) {
      _setError('Failed to update sale: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a sale
  Future<void> deleteSale(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.deleteSale(id);
      await fetchSales();
    } catch (e) {
      _setError('Failed to delete sale: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get sales by customer
  Future<void> getSalesByCustomer(int customerId) async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _saleService.getSalesByCustomer(customerId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get sales by customer: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get sales by date range
  Future<void> getSalesByDateRange(String startDate, String endDate) async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _saleService.getSalesByDateRange(startDate, endDate);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get sales by date range: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get sale items for a specific sale
  Future<List<SaleItem>> getSaleItems(int saleId) async {
    try {
      return await _saleService.getSaleItems(saleId);
    } catch (e) {
      _setError('Failed to get sale items: $e');
      return <SaleItem>[];
    }
  }

  /// Get total sales amount
  Future<double> getTotalSalesAmount() async {
    try {
      return await _saleService.getTotalSalesAmount();
    } catch (e) {
      _setError('Failed to get total sales amount: $e');
      return 0.0;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Create sale with items using transaction
  Future<void> createSaleWithItems(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      // إنشاء المبيعة أولاً
      final int saleId = await _saleService.insertSale(sale);

      // إضافة العناصر
      for (final SaleItem item in items) {
        final SaleItem itemWithSaleId = SaleItem(
          id: item.id,
          saleId: saleId,
          productId: item.productId,
          productName: item.productName,
          quantity: item.quantity,
          price: item.price,
          itemType: item.itemType,
        );
        await _saleService.insertSaleItem(itemWithSaleId);
      }

      await fetchSales();
    } catch (e) {
      _setError('Failed to create sale with items: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete sale and restore stock
  Future<void> deleteSaleAndRestoreStock(int saleId) async {
    _setLoading(true);
    _clearError();

    try {
      // حذف عناصر المبيعة أولاً
      await _saleService.deleteSaleItems(saleId);

      // ثم حذف المبيعة
      await _saleService.deleteSale(saleId);

      await fetchSales();
    } catch (e) {
      _setError('Failed to delete sale and restore stock: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get sale count
  Future<int> getSaleCount() async {
    try {
      return await _saleService.getSaleCount();
    } catch (e) {
      _setError('Failed to get sale count: $e');
      return 0;
    }
  }

  /// مسح جميع المبيعات
  void clearSales() {
    _sales.clear();
    notifyListeners();
  }

  /// إضافة مبيعة مع عناصرها
  Future<Sale> addSale(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      // إنشاء المبيعة مع العناصر
      await createSaleWithItems(sale, items);

      // إعادة تحميل المبيعات
      await fetchSales();

      // العثور على المبيعة المضافة حديثاً
      final Sale addedSale = _sales.isNotEmpty ? _sales.first : sale;
      return addedSale;
    } catch (e) {
      _setError('فشل في إضافة المبيعة: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث مبيعة مع عناصرها
  Future<Sale> updateSale(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      // تحديث المبيعة
      await _saleService.updateSale(sale);

      // حذف العناصر القديمة
      if (sale.id != null) {
        await _saleService.deleteSaleItems(sale.id!);

        // إضافة العناصر الجديدة
        for (final SaleItem item in items) {
          final SaleItem itemWithSaleId = SaleItem(
            id: item.id,
            saleId: sale.id!,
            productId: item.productId,
            productName: item.productName,
            quantity: item.quantity,
            price: item.price,
            itemType: item.itemType,
          );
          await _saleService.insertSaleItem(itemWithSaleId);
        }
      }

      // إعادة تحميل المبيعات
      await fetchSales();

      // العثور على المبيعة المحدثة
      final Sale updatedSale =
          _sales.where((Sale s) => s.id == sale.id).firstOrNull ?? sale;
      return updatedSale;
    } catch (e) {
      _setError('فشل في تحديث المبيعة: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث حالة المبيعة فقط
  Future<void> updateSaleStatus(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.updateSale(sale);
      await fetchSales();
    } catch (e) {
      _setError('فشل في تحديث حالة المبيعة: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة بند إلى الفاتورة مع تحديث المخزون حسب النوع
  Future<void> addSaleItem(SaleItem item) async {
    try {
      // التحقق من نوع البند وتحديث المخزون المناسب
      if (item.itemType == 'wholesale' && item.productId != null) {
        // للبيع بالجملة: تقليل كمية المخزن
        // سيتم تنفيذ هذا في TransactionService عند حفظ الفاتورة
        debugPrint(
            '🏪 بند جملة: ${item.productName} - الكمية: ${item.quantity}');
      } else if (item.itemType == 'retail') {
        // للبيع بالتجزئة: لا نحتاج لتحديث مخزون منتج محدد
        debugPrint('🛒 بند تجزئة: ${item.productName} - المبلغ: ${item.price}');
      }
    } catch (e) {
      _setError('فشل في إضافة البند: $e');
      rethrow;
    }
  }

  /// حساب إجمالي مبيعات الجملة
  double calculateTotalWholesaleAmount(List<SaleItem> items) {
    return items.where((SaleItem item) => item.itemType == 'wholesale').fold(
        0.0,
        (double sum, SaleItem item) =>
            sum + ((item.quantity ?? 0) * (item.price ?? 0)));
  }

  /// حساب إجمالي مبيعات التجزئة
  double calculateTotalRetailAmount(List<SaleItem> items) {
    return items
        .where((SaleItem item) => item.itemType == 'retail')
        .fold(0.0, (double sum, SaleItem item) => sum + (item.price ?? 0));
  }

  /// حساب إجمالي الفاتورة
  double calculateTotalAmount(List<SaleItem> items) {
    return calculateTotalWholesaleAmount(items) +
        calculateTotalRetailAmount(items);
  }

  /// حفظ فاتورة مع الحقول الجديدة
  Future<Sale> saveSale(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      // حفظ الفاتورة في قاعدة البيانات
      await _saleService.insertSale(sale);

      // إعادة تحميل المبيعات
      await fetchSales();

      // العثور على الفاتورة المحفوظة
      final Sale savedSale = _sales.isNotEmpty ? _sales.first : sale;
      return savedSale;
    } catch (e) {
      _setError('فشل في حفظ الفاتورة: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }
}
