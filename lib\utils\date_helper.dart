import 'package:intl/intl.dart';

/// فئة مساعدة للتعامل مع التواريخ
class DateHelper {
  /// الحصول على التاريخ الحالي بدون وقت
  static DateTime get today {
    final DateTime now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }

  /// الحصول على بداية الأسبوع الحالي
  static DateTime get startOfWeek {
    final DateTime now = DateTime.now();
    final int daysFromMonday = now.weekday - 1;
    return DateTime(now.year, now.month, now.day - daysFromMonday);
  }

  /// الحصول على نهاية الأسبوع الحالي
  static DateTime get endOfWeek {
    return startOfWeek.add(const Duration(days: 6));
  }

  /// الحصول على بداية الشهر الحالي
  static DateTime get startOfMonth {
    final DateTime now = DateTime.now();
    return DateTime(now.year, now.month, 1);
  }

  /// الحصول على نهاية الشهر الحالي
  static DateTime get endOfMonth {
    final DateTime now = DateTime.now();
    return DateTime(now.year, now.month + 1, 0);
  }

  /// الحصول على بداية السنة الحالية
  static DateTime get startOfYear {
    final DateTime now = DateTime.now();
    return DateTime(now.year, 1, 1);
  }

  /// الحصول على نهاية السنة الحالية
  static DateTime get endOfYear {
    final DateTime now = DateTime.now();
    return DateTime(now.year, 12, 31);
  }

  /// الحصول على تاريخ الأمس
  static DateTime get yesterday {
    return today.subtract(const Duration(days: 1));
  }

  /// الحصول على تاريخ الغد
  static DateTime get tomorrow {
    return today.add(const Duration(days: 1));
  }

  /// التحقق من كون التاريخ اليوم
  static bool isToday(DateTime date) {
    final DateTime now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// التحقق من كون التاريخ أمس
  static bool isYesterday(DateTime date) {
    final DateTime yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day;
  }

  /// التحقق من كون التاريخ في هذا الأسبوع
  static bool isThisWeek(DateTime date) {
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
        date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  /// التحقق من كون التاريخ في هذا الشهر
  static bool isThisMonth(DateTime date) {
    final DateTime now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  /// التحقق من كون التاريخ في هذه السنة
  static bool isThisYear(DateTime date) {
    final DateTime now = DateTime.now();
    return date.year == now.year;
  }

  /// الحصول على عدد الأيام بين تاريخين
  static int daysBetween(DateTime start, DateTime end) {
    return end.difference(start).inDays;
  }

  /// الحصول على عدد الأسابيع بين تاريخين
  static int weeksBetween(DateTime start, DateTime end) {
    return (end.difference(start).inDays / 7).floor();
  }

  /// الحصول على عدد الأشهر بين تاريخين
  static int monthsBetween(DateTime start, DateTime end) {
    return (end.year - start.year) * 12 + end.month - start.month;
  }

  /// الحصول على عدد السنوات بين تاريخين
  static int yearsBetween(DateTime start, DateTime end) {
    return end.year - start.year;
  }

  /// تحويل النص إلى تاريخ
  static DateTime? parseDate(String dateString, {String? format}) {
    try {
      if (format != null) {
        final DateFormat formatter = DateFormat(format);
        return formatter.parse(dateString);
      } else {
        // محاولة تحليل التنسيقات الشائعة
        final List<String> formats = <String>[
          'yyyy-MM-dd',
          'dd/MM/yyyy',
          'MM/dd/yyyy',
          'yyyy-MM-dd HH:mm:ss',
          'dd/MM/yyyy HH:mm',
        ];

        for (final String fmt in formats) {
          try {
            final DateFormat formatter = DateFormat(fmt);
            return formatter.parse(dateString);
          } catch (e) {
            continue;
          }
        }
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  /// تنسيق التاريخ كنص
  static String formatDate(DateTime date, {String? format}) {
    final DateFormat formatter = DateFormat(format ?? 'dd/MM/yyyy', 'ar_SA');
    return formatter.format(date);
  }

  /// تنسيق التاريخ والوقت كنص
  static String formatDateTime(DateTime dateTime, {String? format}) {
    final DateFormat formatter =
        DateFormat(format ?? 'dd/MM/yyyy HH:mm', 'ar_SA');
    return formatter.format(dateTime);
  }

  /// الحصول على اسم اليوم
  static String getDayName(DateTime date) {
    final DateFormat formatter = DateFormat('EEEE', 'ar_SA');
    return formatter.format(date);
  }

  /// الحصول على اسم الشهر
  static String getMonthName(DateTime date) {
    final DateFormat formatter = DateFormat('MMMM', 'ar_SA');
    return formatter.format(date);
  }

  /// الحصول على التاريخ الهجري (تقريبي)
  static String getHijriDate(DateTime date) {
    // تحويل تقريبي للتاريخ الهجري
    // هذا تحويل مبسط وقد لا يكون دقيقاً 100%
    const int hijriEpoch = 227015; // 1 محرم 1 هـ
    final int julianDay = _toJulianDay(date);
    final int hijriDay = julianDay - hijriEpoch;
    final int hijriYear = ((hijriDay * 30) / 10631).floor() + 1;
    final int remainingDays =
        hijriDay - (((hijriYear - 1) * 10631) / 30).floor();
    final int hijriMonth = ((remainingDays * 12) / 354).floor() + 1;
    final int hijriDayOfMonth =
        (remainingDays - (((hijriMonth - 1) * 354) / 12).floor()) + 1;

    return '${hijriDayOfMonth.floor()}/${hijriMonth.floor()}/$hijriYearهـ';
  }

  /// تحويل التاريخ إلى Julian Day
  static int _toJulianDay(DateTime date) {
    final int a = ((14 - date.month) / 12).floor();
    final int y = date.year + 4800 - a;
    final int m = date.month + 12 * a - 3;

    return date.day +
        ((153 * m + 2) / 5).floor() +
        365 * y +
        (y / 4).floor() -
        (y / 100).floor() +
        (y / 400).floor() -
        32045;
  }

  /// الحصول على قائمة بالأيام في الشهر
  static List<DateTime> getDaysInMonth(DateTime date) {
    final DateTime firstDay = DateTime(date.year, date.month, 1);
    final DateTime lastDay = DateTime(date.year, date.month + 1, 0);
    final List<DateTime> days = <DateTime>[];

    for (int i = 0; i < lastDay.day; i++) {
      days.add(firstDay.add(Duration(days: i)));
    }

    return days;
  }

  /// الحصول على قائمة بالأسابيع في الشهر
  static List<List<DateTime>> getWeeksInMonth(DateTime date) {
    final List<DateTime> days = getDaysInMonth(date);
    final List<List<DateTime>> weeks = <List<DateTime>>[];
    List<DateTime> currentWeek = <DateTime>[];

    // إضافة الأيام الفارغة في بداية الشهر
    final int firstDayWeekday = days.first.weekday;
    for (int i = 1; i < firstDayWeekday; i++) {
      currentWeek.add(days.first.subtract(Duration(days: firstDayWeekday - i)));
    }

    for (final DateTime day in days) {
      currentWeek.add(day);

      if (currentWeek.length == 7) {
        weeks.add(List.from(currentWeek));
        currentWeek.clear();
      }
    }

    // إضافة الأيام الفارغة في نهاية الشهر
    if (currentWeek.isNotEmpty) {
      while (currentWeek.length < 7) {
        currentWeek.add(days.last
            .add(Duration(days: currentWeek.length - days.length + 1)));
      }
      weeks.add(currentWeek);
    }

    return weeks;
  }

  /// الحصول على فترات زمنية محددة مسبقاً
  static Map<String, DateRange> getPredefinedRanges() {
    return <String, DateRange>{
      'اليوم': DateRange(today, today),
      'أمس': DateRange(yesterday, yesterday),
      'هذا الأسبوع': DateRange(startOfWeek, endOfWeek),
      'الأسبوع الماضي': DateRange(
        startOfWeek.subtract(const Duration(days: 7)),
        endOfWeek.subtract(const Duration(days: 7)),
      ),
      'هذا الشهر': DateRange(startOfMonth, endOfMonth),
      'الشهر الماضي': DateRange(
        DateTime(startOfMonth.year, startOfMonth.month - 1, 1),
        DateTime(startOfMonth.year, startOfMonth.month, 0),
      ),
      'هذه السنة': DateRange(startOfYear, endOfYear),
      'السنة الماضية': DateRange(
        DateTime(startOfYear.year - 1, 1, 1),
        DateTime(startOfYear.year - 1, 12, 31),
      ),
      'آخر 7 أيام': DateRange(
        today.subtract(const Duration(days: 6)),
        today,
      ),
      'آخر 30 يوم': DateRange(
        today.subtract(const Duration(days: 29)),
        today,
      ),
      'آخر 90 يوم': DateRange(
        today.subtract(const Duration(days: 89)),
        today,
      ),
    };
  }

  /// التحقق من كون التاريخ في نطاق معين
  static bool isInRange(DateTime date, DateTime start, DateTime end) {
    return date.isAfter(start.subtract(const Duration(days: 1))) &&
        date.isBefore(end.add(const Duration(days: 1)));
  }

  /// الحصول على أقرب يوم عمل
  static DateTime getNextWorkingDay(DateTime date) {
    DateTime nextDay = date;

    while (nextDay.weekday == DateTime.friday ||
        nextDay.weekday == DateTime.saturday) {
      nextDay = nextDay.add(const Duration(days: 1));
    }

    return nextDay;
  }

  /// التحقق من كون اليوم يوم عمل
  static bool isWorkingDay(DateTime date) {
    return date.weekday != DateTime.friday && date.weekday != DateTime.saturday;
  }
}

/// فئة لتمثيل نطاق زمني
class DateRange {
  final DateTime start;
  final DateTime end;

  DateRange(this.start, this.end);

  /// التحقق من كون التاريخ في النطاق
  bool contains(DateTime date) {
    return DateHelper.isInRange(date, start, end);
  }

  /// الحصول على عدد الأيام في النطاق
  int get days => DateHelper.daysBetween(start, end) + 1;

  /// تحويل النطاق إلى نص
  @override
  String toString() {
    return '${DateHelper.formatDate(start)} - ${DateHelper.formatDate(end)}';
  }
}
