# دليل استخدام المزامنة عبر Google Drive

## 📱 ربط هاتفين عبر المزامنة

### الهدف
تمكين المستخدم من مزامنة بيانات التطبيق بين أجهزة متعددة (هاتفين أو أكثر) باستخدام Google Drive كواسطة.

---

## 🔧 المتطلبات الأساسية

### 1. إعداد Google Drive API
- يجب إكمال إعداد Google Cloud Platform وفقاً لـ `GOOGLE_SETUP_INSTRUCTIONS.md`
- تأكد من وجود ملف `google-services.json` في المشروع
- تأكد من تفعيل Google Drive API

### 2. حساب Google مشترك
- يجب استخدام نفس حساب Google على جميع الأجهزة
- تأكد من وجود اتصال إنترنت مستقر

---

## 📋 خطوات المزامنة بين هاتفين

### الجهاز الأول (المصدر):

#### 1. إعداد البيانات
```
1. افتح التطبيق وأدخل البيانات المطلوبة:
   - المنتجات
   - العملاء
   - الموردين
   - المبيعات والمشتريات
   - أي بيانات أخرى
```

#### 2. تسجيل الدخول إلى Google Drive
```
1. اذهب إلى: الإعدادات > النسخ الاحتياطي والاستعادة
2. في قسم "Google Drive"، انقر "تسجيل الدخول"
3. أكمل عملية تسجيل الدخول بحساب Google
4. تأكد من ظهور رسالة "تم تسجيل الدخول بنجاح"
```

#### 3. إنشاء نسخة احتياطية
```
1. في نفس الشاشة، انقر "رفع نسخة احتياطية"
2. انتظر حتى تكتمل العملية
3. تأكد من ظهور رسالة "تم رفع النسخة الاحتياطية إلى Google Drive بنجاح"
4. تحقق من ظهور النسخة الاحتياطية في قائمة "النسخ الاحتياطية على Google Drive"
```

### الجهاز الثاني (الهدف):

#### 1. تسجيل الدخول
```
1. افتح التطبيق على الجهاز الثاني
2. اذهب إلى: الإعدادات > النسخ الاحتياطي والاستعادة
3. سجل الدخول بنفس حساب Google المستخدم في الجهاز الأول
```

#### 2. المزامنة
```
الطريقة الأولى - المزامنة التلقائية:
1. في قسم "المزامنة عبر Google Drive"، انقر "مزامنة الآن"
2. ستظهر رسالة تأكيد تحتوي على:
   - تحذير من حذف البيانات الحالية
   - معلومات النسخة الاحتياطية الأحدث
3. انقر "تأكيد المزامنة"
4. انتظر حتى تكتمل العملية

الطريقة الثانية - المزامنة السريعة:
1. في الشاشة الرئيسية للإعدادات، انقر "مزامنة سريعة"
2. اتبع نفس خطوات التأكيد
```

#### 3. التحقق من النتائج
```
1. بعد اكتمال المزامنة، تحقق من:
   - المنتجات: يجب أن تظهر نفس منتجات الجهاز الأول
   - العملاء والموردين: نفس القوائم
   - المبيعات والمشتريات: نفس السجلات
   - التقارير: نفس البيانات والإحصائيات
```

---

## ⚠️ تحذيرات مهمة

### فقدان البيانات
```
🚨 المزامنة عملية استبدال كاملة:
- ستحذف جميع البيانات الحالية على الجهاز المستقبِل
- ستستبدلها ببيانات النسخة الاحتياطية الأحدث
- لا يمكن التراجع عن هذه العملية
```

### النسخ الاحتياطية المتعددة
```
- النظام يختار تلقائياً أحدث نسخة احتياطية
- تأكد من إنشاء نسخة احتياطية حديثة قبل المزامنة
- يمكن الاستعادة يدوياً من نسخة احتياطية محددة
```

---

## 🔄 سيناريوهات الاستخدام

### السيناريو 1: نقل البيانات لجهاز جديد
```
1. الجهاز القديم: إنشاء نسخة احتياطية حديثة
2. الجهاز الجديد: تسجيل الدخول والمزامنة
3. النتيجة: نفس البيانات على الجهازين
```

### السيناريو 2: مشاركة البيانات بين فريق العمل
```
1. الجهاز الرئيسي: تحديث البيانات وإنشاء نسخة احتياطية
2. أجهزة الفريق: المزامنة للحصول على آخر التحديثات
3. النتيجة: جميع الأجهزة لديها نفس البيانات
```

### السيناريو 3: النسخ الاحتياطي الدوري
```
1. إعداد النسخ الاحتياطي التلقائي
2. المزامنة الدورية على الأجهزة الأخرى
3. النتيجة: حماية البيانات ومزامنة مستمرة
```

---

## 🛠️ استكشاف الأخطاء

### خطأ "لا توجد نسخ احتياطية"
```
الحل:
1. تأكد من تسجيل الدخول بنفس الحساب
2. أنشئ نسخة احتياطية من الجهاز المصدر
3. تحقق من اتصال الإنترنت
```

### خطأ "فشل في المزامنة"
```
الحل:
1. تحقق من اتصال الإنترنت
2. تأكد من صحة تسجيل الدخول
3. أعد تسجيل الدخول إذا لزم الأمر
4. تحقق من أذونات Google Drive
```

### خطأ "تسجيل الدخول فشل"
```
الحل:
1. تحقق من إعداد Google Services
2. تأكد من صحة ملف google-services.json
3. تحقق من SHA-1 fingerprint
4. راجع GOOGLE_SETUP_INSTRUCTIONS.md
```

---

## 📊 مراقبة حالة المزامنة

### معلومات المزامنة
```
- آخر مزامنة: التاريخ والوقت
- حالة المزامنة: نجحت/فشلت/ملغاة
- عدد النسخ الاحتياطية المتاحة
```

### مؤشرات الحالة
```
- أيقونة المزامنة: تظهر عند توفر المزامنة
- مؤشر التحميل: يظهر أثناء العملية
- رسائل النجاح/الخطأ: تظهر بعد كل عملية
```

---

## 🔐 الأمان والخصوصية

### حماية البيانات
```
- البيانات محمية بتشفير Google Drive
- الوصول محدود بحساب Google المصرح
- لا يمكن الوصول للبيانات بدون تسجيل الدخول
```

### أفضل الممارسات
```
1. استخدم حساب Google آمن مع تفعيل المصادقة الثنائية
2. لا تشارك بيانات تسجيل الدخول
3. أنشئ نسخ احتياطية دورية
4. تحقق من صحة البيانات بعد كل مزامنة
```

---

## 📱 اختبار النظام

### ملف الاختبار
```
استخدم test_sync_system.dart لاختبار:
- تسجيل الدخول/الخروج
- إنشاء النسخ الاحتياطية
- المزامنة
- عرض حالة النظام
```

### خطوات الاختبار
```
1. شغل ملف الاختبار
2. اختبر جميع الوظائف
3. تحقق من الرسائل والحالات
4. تأكد من عمل المزامنة بشكل صحيح
```

---

## 📞 الدعم الفني

### في حالة المشاكل
```
1. راجع هذا الدليل
2. تحقق من GOOGLE_SETUP_INSTRUCTIONS.md
3. استخدم ملف الاختبار للتشخيص
4. تحقق من سجلات التطبيق (Debug Console)
```

### معلومات مفيدة للدعم
```
- نوع الخطأ ورسالته
- خطوات إعادة إنتاج المشكلة
- معلومات الجهاز ونظام التشغيل
- حالة تسجيل الدخول وإعدادات Google
```
