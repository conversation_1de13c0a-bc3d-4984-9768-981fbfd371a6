/// اختبارات المزودات (Provider Tests) لتطبيق أسامة ماركت
///
/// تتضمن اختبارات لجميع المزودات والحالة العامة
/// للتأكد من عمل إدارة الحالة بشكل صحيح
library provider_tests;

import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/providers/backup_provider.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/services/database_service.dart';

void main() {
  // إعداد قاعدة البيانات واللغة للاختبار قبل جميع الاختبارات
  setUpAll(() async {
    // تهيئة بيانات اللغة العربية
    await initializeDateFormatting('ar', null);
    await initializeDateFormatting('ar_SA', null);

    // تفعيل وضع الاختبار مع قاعدة بيانات في الذاكرة
    DatabaseService.enableTestMode();

    // تهيئة قاعدة البيانات
    await DatabaseService.instance.database;
  });

  // تنظيف قاعدة البيانات بعد كل اختبار
  tearDown(() async {
    await DatabaseService.instance.clearDatabaseForTesting();
  });

  // إلغاء تفعيل وضع الاختبار بعد جميع الاختبارات
  tearDownAll(() async {
    DatabaseService.disableTestMode();
    await DatabaseService.instance.resetDatabase();
  });

  group('اختبارات مزود المنتجات (ProductProvider)', () {
    late ProductProvider productProvider;

    setUp(() {
      productProvider = ProductProvider();
    });

    test('يجب أن يبدأ بقائمة فارغة من المنتجات', () {
      expect(productProvider.products, isEmpty);
      expect(productProvider.isLoading, isFalse);
    });

    test('يجب أن يضيف منتج جديد بشكل صحيح', () async {
      // إعداد قاعدة البيانات للاختبار
      await DatabaseService.instance.database;

      final Product testProduct = Product(
        name: 'منتج اختبار',
        price: 10.0, // السعر الأساسي (الجملة)
        barcode: '123456789',
        category: 'فئة اختبار',
        unit: 'قطعة',
        warehouseQuantity: 100,
        storeQuantity: 50,
        retailPrice: 15.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // إضافة المنتج
      await productProvider.addProduct(testProduct);

      // التحقق من إضافة المنتج
      expect(productProvider.products.length, equals(1));
      expect(productProvider.products.first.name, equals('منتج اختبار'));
    });

    test('يجب أن يحدث المنتج بشكل صحيح', () async {
      // إعداد قاعدة البيانات للاختبار
      await DatabaseService.instance.database;

      // إضافة منتج أولاً
      final Product testProduct = Product(
        name: 'منتج اختبار',
        price: 10.0, // السعر الأساسي (الجملة)
        barcode: '123456789',
        category: 'فئة اختبار',
        unit: 'قطعة',
        warehouseQuantity: 100,
        storeQuantity: 50,
        retailPrice: 15.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await productProvider.addProduct(testProduct);
      final Product addedProduct = productProvider.products.first;

      // تحديث المنتج
      final Product updatedProduct = addedProduct.copyWith(
        name: 'منتج محدث',
        retailPrice: 20.0,
      );

      await productProvider.updateProduct(updatedProduct);

      // التحقق من التحديث
      expect(productProvider.products.first.name, equals('منتج محدث'));
      expect(productProvider.products.first.retailPrice, equals(20.0));
    });

    test('يجب أن يحذف المنتج بشكل صحيح', () async {
      // إعداد قاعدة البيانات للاختبار
      await DatabaseService.instance.database;

      // إضافة منتج أولاً
      final Product testProduct = Product(
        name: 'منتج للحذف',
        price: 10.0, // السعر الأساسي (الجملة)
        barcode: '987654321',
        category: 'فئة اختبار',
        unit: 'قطعة',
        warehouseQuantity: 100,
        storeQuantity: 50,
        retailPrice: 15.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await productProvider.addProduct(testProduct);
      expect(productProvider.products.length, equals(1));

      // حذف المنتج
      final int productId = productProvider.products.first.id!;
      await productProvider.deleteProduct(productId);

      // التحقق من الحذف
      expect(productProvider.products, isEmpty);
    });
  });

  group('اختبارات مزود العملاء (CustomerProvider)', () {
    late CustomerProvider customerProvider;

    setUp(() {
      customerProvider = CustomerProvider();
    });

    test('يجب أن يبدأ بقائمة فارغة من العملاء', () {
      expect(customerProvider.customers, isEmpty);
      expect(customerProvider.isLoading, isFalse);
    });

    test('يجب أن يضيف عميل جديد بشكل صحيح', () async {
      // إعداد قاعدة البيانات للاختبار
      await DatabaseService.instance.database;

      final Customer testCustomer = Customer(
        name: 'عميل اختبار',
        phone: '**********',
        address: 'عنوان اختبار',
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // إضافة العميل
      await customerProvider.addCustomer(testCustomer);

      // التحقق من إضافة العميل
      expect(customerProvider.customers.length, equals(1));
      expect(customerProvider.customers.first.name, equals('عميل اختبار'));
    });

    test('يجب أن يحدث رصيد العميل بشكل صحيح', () async {
      // إعداد قاعدة البيانات للاختبار
      await DatabaseService.instance.database;

      // إضافة عميل أولاً
      final Customer testCustomer = Customer(
        name: 'عميل اختبار',
        phone: '**********',
        address: 'عنوان اختبار',
        balance: 100.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await customerProvider.addCustomer(testCustomer);
      final Customer addedCustomer = customerProvider.customers.first;

      // تحديث العميل مع رصيد جديد
      final Customer updatedCustomer = addedCustomer.copyWith(balance: 150.0);
      await customerProvider.updateCustomer(updatedCustomer);

      // التحقق من التحديث
      expect(customerProvider.customers.first.balance, equals(150.0));
    });
  });

  group('اختبارات مزود المبيعات (SaleProvider)', () {
    late SaleProvider saleProvider;

    setUp(() {
      saleProvider = SaleProvider();
    });

    test('يجب أن يبدأ بقائمة فارغة من المبيعات', () {
      expect(saleProvider.sales, isEmpty);
      expect(saleProvider.isLoading, isFalse);
    });

    test('يجب أن يحسب إجمالي المبيعات بشكل صحيح', () {
      // إضافة مبيعات وهمية للاختبار
      final List<Sale> testSales = [
        Sale(
          id: 1,
          customerId: 1,
          customerName: 'عميل 1',
          totalAmount: 100.0,
          paidAmount: 100.0,
          remainingAmount: 0.0,
          saleType: 'retail',
          paymentMethod: 'cash',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Sale(
          id: 2,
          customerId: 2,
          customerName: 'عميل 2',
          totalAmount: 200.0,
          paidAmount: 150.0,
          remainingAmount: 50.0,
          saleType: 'wholesale',
          paymentMethod: 'credit',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      // حساب الإجمالي
      final double totalSales =
          testSales.fold(0.0, (sum, sale) => sum + (sale.totalAmount ?? 0.0));

      expect(totalSales, equals(300.0));
    });

    test('يجب أن يحسب المبلغ المتبقي الإجمالي بشكل صحيح', () {
      // إضافة مبيعات وهمية للاختبار
      final List<Sale> testSales = [
        Sale(
          id: 1,
          customerId: 1,
          customerName: 'عميل 1',
          totalAmount: 100.0,
          paidAmount: 80.0,
          remainingAmount: 20.0,
          saleType: 'retail',
          paymentMethod: 'cash',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Sale(
          id: 2,
          customerId: 2,
          customerName: 'عميل 2',
          totalAmount: 200.0,
          paidAmount: 150.0,
          remainingAmount: 50.0,
          saleType: 'wholesale',
          paymentMethod: 'credit',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      // حساب المبلغ المتبقي الإجمالي
      final double totalRemaining = testSales.fold(
          0.0, (sum, sale) => sum + (sale.remainingAmount ?? 0.0));

      expect(totalRemaining, equals(70.0));
    });
  });

  group('اختبارات مزود النسخ الاحتياطي (BackupProvider)', () {
    late BackupProvider backupProvider;

    setUp(() {
      backupProvider = BackupProvider();
    });

    test('يجب أن يبدأ بحالة غير محمل', () {
      expect(backupProvider.isLoading, isFalse);
      expect(backupProvider.isSignedIn, isFalse);
    });

    test('يجب أن يغير حالة التحميل بشكل صحيح', () {
      expect(backupProvider.isLoading, isFalse);

      // اختبار أن الحالة الافتراضية هي عدم التحميل
      expect(backupProvider.isLoading, isFalse);

      // يمكن إضافة اختبارات أخرى للنسخ الاحتياطي هنا
      // مثل اختبار تكرار النسخ الاحتياطي أو إعدادات النسخ
    });
  });
}
