import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../config/app_colors.dart';

import '../providers/product_provider.dart';
import '../providers/sale_provider.dart';
import '../providers/purchase_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/supplier_provider.dart';

/// قسم الإحصائيات مع البطاقات الأربع
class StatsSection extends StatelessWidget {
  const StatsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Padding(
          padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
          child: Text(
            'الإحصائيات',
            style: TextStyle(
              fontFamily: 'Tajawal',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Consumer5<ProductProvider, SaleProvider, PurchaseProvider,
              CustomerProvider, SupplierProvider>(
            builder: (BuildContext context,
                ProductProvider productProvider,
                SaleProvider saleProvider,
                PurchaseProvider purchaseProvider,
                CustomerProvider customerProvider,
                SupplierProvider supplierProvider,
                Widget? child) {
              return GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 1.5,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                children: <Widget>[
                  _buildStatCard(
                    title: 'إجمالي المنتجات',
                    value: '${productProvider.products.length}',
                    icon: Icons.inventory_2,
                    color: AppColors.primary,
                    subtitle: 'منتج متاح',
                    onTap: () {
                      Navigator.of(context).pop(); // إغلاق الـ drawer
                      context.go('/products');
                    },
                    isLoading: productProvider.isLoading,
                  ),
                  _buildStatCard(
                    title: 'العملاء',
                    value: '${customerProvider.customers.length}',
                    icon: Icons.people,
                    color: AppColors.secondary,
                    subtitle: 'عميل مسجل',
                    onTap: () {
                      Navigator.of(context).pop(); // إغلاق الـ drawer
                      context.go('/customers');
                    },
                    isLoading: customerProvider.isLoading,
                  ),
                  _buildStatCard(
                    title: 'المبيعات اليوم',
                    value: '${saleProvider.sales.length}',
                    icon: Icons.point_of_sale,
                    color: AppColors.accent,
                    subtitle: 'عملية بيع',
                    onTap: () {
                      Navigator.of(context).pop(); // إغلاق الـ drawer
                      context.go('/sales');
                    },
                    isLoading: saleProvider.isLoading,
                  ),
                  _buildStatCard(
                    title: 'الموردين',
                    value: '${supplierProvider.suppliers.length}',
                    icon: Icons.local_shipping,
                    color: AppColors.info,
                    subtitle: 'مورد نشط',
                    onTap: () {
                      Navigator.of(context).pop(); // إغلاق الـ drawer
                      context.go('/suppliers');
                    },
                    isLoading: supplierProvider.isLoading,
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String subtitle,
    required VoidCallback onTap,
    bool isLoading = false,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Icon(
                icon,
                color: color,
                size: 32,
              ),
              const SizedBox(height: 8),
              if (isLoading)
                const SizedBox(
                  height: 16,
                  width: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 10,
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
