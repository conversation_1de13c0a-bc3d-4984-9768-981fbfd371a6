import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/expense_provider.dart';
import '../../models/expense.dart';

class CreateExpenseScreen extends StatefulWidget {
  final Expense? expense; // للتعديل

  const CreateExpenseScreen({
    super.key,
    this.expense,
  });

  @override
  State<CreateExpenseScreen> createState() => _CreateExpenseScreenState();
}

class _CreateExpenseScreenState extends State<CreateExpenseScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  String? _selectedCategory;
  bool _isLoading = false;
  bool _isEditMode = false;

  final List<Map<String, String>> _categories = <Map<String, String>>[
    <String, String>{'value': 'rent', 'label': 'إيجار'},
    <String, String>{'value': 'salaries', 'label': 'رواتب'},
    <String, String>{'value': 'supplies', 'label': 'مستلزمات'},
    <String, String>{'value': 'utilities', 'label': 'خدمات'},
    <String, String>{'value': 'maintenance', 'label': 'صيانة'},
    <String, String>{'value': 'marketing', 'label': 'تسويق'},
    <String, String>{'value': 'transportation', 'label': 'نقل ومواصلات'},
    <String, String>{'value': 'miscellaneous', 'label': 'متنوعة'},
  ];

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.expense != null;
    _initializeForm();
  }

  void _initializeForm() {
    if (_isEditMode && widget.expense != null) {
      final Expense expense = widget.expense!;
      _descriptionController.text = expense.description ?? '';
      _amountController.text = expense.amount?.toString() ?? '';
      _notesController.text = expense.notes ?? '';
      _selectedCategory = expense.category;
      _selectedDate = expense.expenseDate ?? DateTime.now();
    }
    _dateController.text = _formatDate(_selectedDate);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(_isEditMode ? 'تعديل مصروف' : 'إضافة مصروف جديد'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveExpense,
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // التاريخ
                TextFormField(
                  controller: _dateController,
                  decoration: const InputDecoration(
                    labelText: 'التاريخ',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: _selectDate,
                ),

                const SizedBox(height: 16),

                // الوصف
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'وصف المصروف *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  validator: (String? value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال وصف المصروف';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // المبلغ
                TextFormField(
                  controller: _amountController,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.attach_money),
                    suffixText: 'ر.س',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (String? value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال المبلغ';
                    }
                    if (double.tryParse(value) == null) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    if (double.parse(value) <= 0) {
                      return 'يجب أن يكون المبلغ أكبر من صفر';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // الفئة
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'فئة المصروف *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.category),
                  ),
                  items: _categories.map((Map<String, String> category) {
                    return DropdownMenuItem(
                      value: category['value'],
                      child: Text(category['label']!),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    setState(() {
                      _selectedCategory = value;
                    });
                  },
                  validator: (String? value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى اختيار فئة المصروف';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // الملاحظات
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.note),
                  ),
                  maxLines: 3,
                ),

                const SizedBox(height: 32),

                // زر الحفظ
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveExpense,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Text(
                            _isEditMode ? 'حفظ التعديلات' : 'حفظ المصروف',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = _formatDate(picked);
      });
    }
  }

  Future<void> _saveExpense() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // عرض تأكيد
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(_isEditMode ? 'تأكيد التعديل' : 'تأكيد الحفظ'),
        content: Text(_isEditMode
            ? 'هل أنت متأكد من حفظ تعديلات المصروف؟'
            : 'هل أنت متأكد من حفظ المصروف؟'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final Expense expense = Expense(
        id: _isEditMode ? widget.expense!.id : null,
        expenseDate: _selectedDate,
        description: _descriptionController.text.trim(),
        amount: double.parse(_amountController.text.trim()),
        category: _selectedCategory,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        status: 'active',
      );

      if (_isEditMode) {
        await context.read<ExpenseProvider>().updateExpense(expense);
      } else {
        await context.read<ExpenseProvider>().addExpense(expense);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditMode
                ? 'تم تعديل المصروف بنجاح'
                : 'تم إضافة المصروف بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    super.dispose();
  }
}
