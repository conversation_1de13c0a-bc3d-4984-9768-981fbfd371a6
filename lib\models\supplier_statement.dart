/// Model class representing a supplier account statement entry
class SupplierStatement {
  /// Unique identifier for the statement entry
  int? id;
  
  /// Supplier ID this statement belongs to
  int? supplierId;
  
  /// Date of the transaction
  String? date;
  
  /// Description of the transaction
  String? description;
  
  /// Amount of the transaction
  double? amount;
  
  /// Running balance after this transaction
  double? balance;

  /// Constructor for creating a SupplierStatement instance
  SupplierStatement({
    this.id,
    this.supplierId,
    this.date,
    this.description,
    this.amount,
    this.balance,
  });

  /// Converts the SupplierStatement instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'supplierId': supplierId,
      'date': date,
      'description': description,
      'amount': amount,
      'balance': balance,
    };
  }

  /// Creates a SupplierStatement instance from a Map (typically from database)
  factory SupplierStatement.fromMap(Map<String, dynamic> map) {
    return SupplierStatement(
      id: map['id'] as int?,
      supplierId: map['supplierId'] as int?,
      date: map['date'] as String?,
      description: map['description'] as String?,
      amount: map['amount']?.toDouble(),
      balance: map['balance']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'SupplierStatement{id: $id, supplierId: $supplierId, date: $date, '
        'description: $description, amount: $amount, balance: $balance}';
  }
}
