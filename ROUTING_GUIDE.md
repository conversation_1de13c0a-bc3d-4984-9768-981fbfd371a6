# دليل نظام التوجيه الموحد

## 🧭 **كيفية استخدام نظام التوجيه**

### **✅ الطريقة الصحيحة للتنقل:**

```dart
import 'package:go_router/go_router.dart';

// التنقل إلى شاشة جديدة
context.go('/products');

// التنقل مع استبدال الشاشة الحالية
context.go('/dashboard');

// التنقل مع معاملات
context.go('/products/edit/123');

// العودة للشاشة السابقة
context.pop();

// التنقل مع إرسال بيانات
context.go('/products/details/123');
```

### **❌ تجنب هذا:**

```dart
// لا تستخدم Navigator مباشرة
Navigator.push(context, MaterialPageRoute(...)); // خطأ!

// لا تستخدم routes قديمة
Navigator.pushNamed(context, '/products'); // خطأ!
```

## 📍 **قائمة المسارات المتاحة**

### **الشاشات الأساسية:**
- `/` - الشاشة الرئيسية (Dashboard)
- `/splash` - شاشة البداية
- `/onboarding` - التعريف بالتطبيق

### **إدارة المنتجات:**
- `/products` - قائمة المنتجات
- `/products/add` - إضافة منتج جديد
- `/products/edit/:id` - تعديل منتج (مثال: `/products/edit/123`)
- `/products/details/:id` - تفاصيل منتج

### **إدارة العملاء:**
- `/customers` - قائمة العملاء
- `/customers/add` - إضافة عميل جديد
- `/customers/edit/:id` - تعديل عميل

### **إدارة الموردين:**
- `/suppliers` - قائمة الموردين

### **المبيعات والمشتريات:**
- `/sales` - قائمة المبيعات
- `/purchases` - قائمة المشتريات
- `/orders` - قائمة الطلبات

### **الفواتير:**
- `/invoices/sales` - فواتير المبيعات
- `/invoices/purchases` - فواتير المشتريات

### **الكشوفات:**
- `/statements/customers` - كشوفات العملاء
- `/statements/suppliers` - كشوفات الموردين

### **إدارة المخزون:**
- `/inventory/transfer` - نقل المخزون الداخلي
- `/inventory/adjustment` - تعديل مخزون المتجر

### **الإعدادات والتقارير:**
- `/categories` - إدارة الفئات
- `/units` - إدارة الوحدات
- `/expenses` - إدارة المصروفات
- `/reports` - التقارير
- `/analytics` - التحليلات
- `/activities` - سجل الأنشطة
- `/settings` - الإعدادات العامة
- `/settings/backup` - إعدادات النسخ الاحتياطي

## 🔧 **إضافة مسار جديد**

### **الخطوات:**

1. **أضف import للشاشة الجديدة في `app_router.dart`:**
```dart
import '../screens/new_feature/new_screen.dart';
```

2. **أضف المسار في قائمة routes:**
```dart
GoRoute(
  path: '/new-feature',
  builder: (BuildContext context, GoRouterState state) =>
      const NewScreen(),
),
```

3. **للمسارات مع معاملات:**
```dart
GoRoute(
  path: '/new-feature/:id',
  builder: (BuildContext context, GoRouterState state) {
    final String id = state.pathParameters['id'] ?? '';
    return NewScreen(id: id);
  },
),
```

4. **للمسارات مع query parameters:**
```dart
GoRoute(
  path: '/new-feature',
  builder: (BuildContext context, GoRouterState state) {
    final String? filter = state.uri.queryParameters['filter'];
    return NewScreen(filter: filter);
  },
),
```

## 🎯 **أمثلة عملية**

### **مثال 1: التنقل من Dashboard إلى المنتجات**
```dart
class DashboardScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () => context.go('/products'),
      child: Text('عرض المنتجات'),
    );
  }
}
```

### **مثال 2: تعديل منتج**
```dart
class ProductListScreen extends StatelessWidget {
  void editProduct(BuildContext context, String productId) {
    context.go('/products/edit/$productId');
  }
  
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemBuilder: (context, index) {
        return ListTile(
          title: Text('منتج $index'),
          onTap: () => editProduct(context, index.toString()),
        );
      },
    );
  }
}
```

### **مثال 3: العودة للشاشة السابقة**
```dart
class ProductFormScreen extends StatelessWidget {
  void saveAndGoBack(BuildContext context) {
    // حفظ البيانات
    // ...
    
    // العودة للشاشة السابقة
    context.pop();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      // ...
    );
  }
}
```

## 🚨 **نصائح مهمة**

1. **استخدم أسماء مسارات واضحة** تعبر عن محتوى الشاشة
2. **تجنب المسارات العميقة جداً** (أكثر من 3 مستويات)
3. **استخدم معاملات للبيانات الديناميكية** مثل IDs
4. **اختبر جميع المسارات** بعد إضافة مسار جديد
5. **حدث التوثيق** عند إضافة مسارات جديدة

## 🔍 **استكشاف الأخطاء**

### **مشكلة: الشاشة لا تظهر**
- تأكد من إضافة import للشاشة
- تأكد من صحة اسم المسار
- تأكد من وجود const constructor

### **مشكلة: معاملات لا تصل**
- تأكد من استخدام `:parameter` في المسار
- تأكد من استخدام `state.pathParameters['parameter']`

### **مشكلة: التنقل لا يعمل**
- تأكد من استخدام `context.go()` وليس `Navigator`
- تأكد من أن المسار موجود في app_router.dart

---

**تذكر:** نظام التوجيه الموحد يجعل التنقل أسهل وأكثر تنظيماً! 🧭✨
