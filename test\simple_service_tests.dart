/// اختبارات مبسطة للخدمات - أسامة ماركت
///
/// تركز على الوظائف الأساسية للخدمات والتأكد من عمل قاعدة البيانات
library simple_service_tests;

import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  // تهيئة بيانات اللغة وقاعدة البيانات للاختبار
  setUpAll(() async {
    await initializeDateFormatting('ar', null);
    await initializeDateFormatting('ar_SA', null);

    // تهيئة databaseFactory للاختبارات
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;

    // تفعيل وضع الاختبار مع قاعدة بيانات في الذاكرة
    DatabaseService.enableTestMode();
    await DatabaseService.instance.database;
  });

  // تنظيف قاعدة البيانات بعد كل اختبار
  tearDown(() async {
    await DatabaseService.instance.clearDatabaseForTesting();
  });

  // إغلاق قاعدة البيانات بعد جميع الاختبارات
  tearDownAll(() async {
    DatabaseService.disableTestMode();
    await DatabaseService.instance.resetDatabase();
  });

  group('اختبارات خدمة قاعدة البيانات الأساسية', () {
    test('يجب أن تتصل بقاعدة البيانات بنجاح', () async {
      final Database database = await DatabaseService.instance.database;
      expect(database, isNotNull);
      expect(database.isOpen, isTrue);
    });

    test('يجب أن تنشئ الجداول الأساسية', () async {
      final Database database = await DatabaseService.instance.database;

      // التحقق من وجود جدول المنتجات
      final List<Map<String, dynamic>> productTables = await database.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='products'");
      expect(productTables.isNotEmpty, isTrue);

      // التحقق من وجود جدول العملاء
      final List<Map<String, dynamic>> customerTables = await database.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='customers'");
      expect(customerTables.isNotEmpty, isTrue);

      // التحقق من وجود جدول المبيعات
      final List<Map<String, dynamic>> salesTables = await database.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='sales'");
      expect(salesTables.isNotEmpty, isTrue);
    });

    test('يجب أن تنظف البيانات بشكل صحيح', () async {
      final Database database = await DatabaseService.instance.database;

      // إضافة بيانات وهمية باستخدام الحقول الموجودة فعلياً في الجدول
      await database.insert('products', {
        'name': 'منتج اختبار',
        'price': 10.0,
        'description': 'وصف المنتج',
        'quantity': 100,
        'retailPrice': 15.0,
        'warehouseQuantity': 100,
        'storeQuantity': 50,
      });

      // التحقق من وجود البيانات
      final List<Map<String, dynamic>> beforeClean =
          await database.query('products');
      expect(beforeClean.length, equals(1));

      // تنظيف البيانات
      await DatabaseService.instance.clearDatabaseForTesting();

      // التحقق من حذف البيانات
      final List<Map<String, dynamic>> afterClean =
          await database.query('products');
      expect(afterClean.length, equals(0));
    });
  });

  group('اختبارات العمليات الأساسية على النماذج', () {
    test('يجب أن تضيف وتسترجع منتج بنجاح', () async {
      final Database database = await DatabaseService.instance.database;

      // إضافة منتج مباشرة باستخدام الحقول الموجودة في الجدول
      final Map<String, dynamic> productData = {
        'name': 'منتج اختبار قاعدة البيانات',
        'price': 25.0,
        'description': 'وصف المنتج',
        'quantity': 200,
        'retailPrice': 35.0,
        'warehouseQuantity': 200,
        'storeQuantity': 100,
      };

      // إضافة المنتج إلى قاعدة البيانات
      final int productId = await database.insert('products', productData);
      expect(productId, greaterThan(0));

      // استرجاع المنتج
      final List<Map<String, dynamic>> retrievedProducts = await database
          .query('products', where: 'id = ?', whereArgs: [productId]);
      expect(retrievedProducts.length, equals(1));

      final Map<String, dynamic> retrievedProduct = retrievedProducts.first;
      expect(retrievedProduct['name'], equals('منتج اختبار قاعدة البيانات'));
      expect(retrievedProduct['price'], equals(25.0));
      expect(retrievedProduct['description'], equals('وصف المنتج'));
    });

    test('يجب أن تضيف وتسترجع عميل بنجاح', () async {
      final Database database = await DatabaseService.instance.database;

      // إنشاء عميل اختبار
      final Customer testCustomer = Customer(
        name: 'عميل اختبار قاعدة البيانات',
        phone: '0501234567',
        email: '<EMAIL>',
        address: 'عنوان اختبار',
        balance: 100.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        notes: 'عميل مميز للاختبار',
      );

      // إضافة العميل إلى قاعدة البيانات
      final int customerId =
          await database.insert('customers', testCustomer.toMap());
      expect(customerId, greaterThan(0));

      // استرجاع العميل
      final List<Map<String, dynamic>> retrievedCustomers = await database
          .query('customers', where: 'id = ?', whereArgs: [customerId]);
      expect(retrievedCustomers.length, equals(1));

      final Customer retrievedCustomer =
          Customer.fromMap(retrievedCustomers.first);
      expect(retrievedCustomer.name, equals('عميل اختبار قاعدة البيانات'));
      expect(retrievedCustomer.phone, equals('0501234567'));
      expect(retrievedCustomer.balance, equals(100.0));
    });

    test('يجب أن تضيف وتسترجع بيع بنجاح', () async {
      final Database database = await DatabaseService.instance.database;

      // إنشاء بيع اختبار
      final Sale testSale = Sale(
        customerId: 1,
        customerName: 'عميل اختبار',
        total: 150.0,
        paidAmount: 100.0,
        remainingAmount: 50.0,
        saleType: 'retail',
        paymentMethod: 'cash',
        status: 'completed',
        date: DateTime.now().toIso8601String(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // إضافة البيع إلى قاعدة البيانات
      final int saleId = await database.insert('sales', testSale.toMap());
      expect(saleId, greaterThan(0));

      // استرجاع البيع
      final List<Map<String, dynamic>> retrievedSales =
          await database.query('sales', where: 'id = ?', whereArgs: [saleId]);
      expect(retrievedSales.length, equals(1));

      final Sale retrievedSale = Sale.fromMap(retrievedSales.first);
      expect(retrievedSale.customerName, equals('عميل اختبار'));
      expect(retrievedSale.total, equals(150.0));
      expect(retrievedSale.saleType, equals('retail'));
    });
  });

  group('اختبارات العمليات المتقدمة', () {
    test('يجب أن تحدث بيانات المنتج بنجاح', () async {
      final Database database = await DatabaseService.instance.database;

      // إضافة منتج
      final Product originalProduct = Product(
        name: 'منتج للتحديث',
        price: 20.0,
        barcode: '111222333',
        category: 'فئة أصلية',
        unit: 'قطعة',
        warehouseQuantity: 100,
        storeQuantity: 50,
        retailPrice: 30.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int productId =
          await database.insert('products', originalProduct.toMap());

      // تحديث المنتج
      final Product updatedProduct = originalProduct.copyWith(
        id: productId,
        name: 'منتج محدث',
        price: 25.0,
        category: 'فئة محدثة',
        updatedAt: DateTime.now(),
      );

      final int updatedRows = await database.update(
        'products',
        updatedProduct.toMap(),
        where: 'id = ?',
        whereArgs: [productId],
      );
      expect(updatedRows, equals(1));

      // التحقق من التحديث
      final List<Map<String, dynamic>> retrievedProducts = await database
          .query('products', where: 'id = ?', whereArgs: [productId]);
      final Product retrievedProduct = Product.fromMap(retrievedProducts.first);

      expect(retrievedProduct.name, equals('منتج محدث'));
      expect(retrievedProduct.price, equals(25.0));
      expect(retrievedProduct.category, equals('فئة محدثة'));
    });

    test('يجب أن تحذف بيانات بنجاح', () async {
      final Database database = await DatabaseService.instance.database;

      // إضافة عميل
      final Customer testCustomer = Customer(
        name: 'عميل للحذف',
        phone: '0509876543',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int customerId =
          await database.insert('customers', testCustomer.toMap());

      // التحقق من وجود العميل
      List<Map<String, dynamic>> customers = await database
          .query('customers', where: 'id = ?', whereArgs: [customerId]);
      expect(customers.length, equals(1));

      // حذف العميل
      final int deletedRows = await database.delete(
        'customers',
        where: 'id = ?',
        whereArgs: [customerId],
      );
      expect(deletedRows, equals(1));

      // التحقق من الحذف
      customers = await database
          .query('customers', where: 'id = ?', whereArgs: [customerId]);
      expect(customers.length, equals(0));
    });

    test('يجب أن تحسب إجماليات بشكل صحيح', () async {
      final Database database = await DatabaseService.instance.database;

      // إضافة عدة مبيعات
      final List<Sale> testSales = [
        Sale(
          customerId: 1,
          customerName: 'عميل 1',
          total: 100.0,
          paidAmount: 100.0,
          remainingAmount: 0.0,
          saleType: 'retail',
          date: DateTime.now().toIso8601String(),
          createdAt: DateTime.now(),
        ),
        Sale(
          customerId: 2,
          customerName: 'عميل 2',
          total: 200.0,
          paidAmount: 150.0,
          remainingAmount: 50.0,
          saleType: 'wholesale',
          date: DateTime.now().toIso8601String(),
          createdAt: DateTime.now(),
        ),
        Sale(
          customerId: 3,
          customerName: 'عميل 3',
          total: 150.0,
          paidAmount: 100.0,
          remainingAmount: 50.0,
          saleType: 'retail',
          date: DateTime.now().toIso8601String(),
          createdAt: DateTime.now(),
        ),
      ];

      // إضافة المبيعات إلى قاعدة البيانات
      for (final Sale sale in testSales) {
        await database.insert('sales', sale.toMap());
      }

      // حساب إجمالي المبيعات
      final List<Map<String, dynamic>> totalResult =
          await database.rawQuery('SELECT SUM(total) as totalSales FROM sales');
      final double totalSales =
          totalResult.first['totalSales'] as double? ?? 0.0;
      expect(totalSales, equals(450.0));

      // حساب إجمالي المبلغ المتبقي
      final List<Map<String, dynamic>> remainingResult = await database
          .rawQuery('SELECT SUM(remainingAmount) as totalRemaining FROM sales');
      final double totalRemaining =
          remainingResult.first['totalRemaining'] as double? ?? 0.0;
      expect(totalRemaining, equals(100.0));
    });
  });
}
