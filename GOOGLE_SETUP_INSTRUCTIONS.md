# تعليمات إعداد Google Drive API للنسخ الاحتياطي

## ⚠️ مهم: يج<PERSON> إكمال هذه الخطوات قبل تشغيل التطبيق

لتفعيل ميزة النسخ الاحتياطي عبر Google Drive، يجب إعداد Google Cloud Platform وGoogle Sign-In.

## 1. إنشاء مشروع في Google Cloud Platform

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. تأكد من تفعيل الفوترة للمشروع

## 2. تفعيل Google Drive API

1. في Google Cloud Console، اذهب إلى "APIs & Services" > "Library"
2. ابحث عن "Google Drive API"
3. انقر على "Enable"

## 3. إنش<PERSON>ء OAuth 2.0 Credentials

### للأندرويد:
1. اذ<PERSON><PERSON> إلى "APIs & Services" > "Credentials"
2. ا<PERSON><PERSON><PERSON> على "Create Credentials" > "OAuth client ID"
3. اختر "Android"
4. أدخل Package name: `com.example.inventory_management_app`
5. احصل على SHA-1 fingerprint:
   ```bash
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   ```
6. أدخل SHA-1 fingerprint
7. انقر على "Create"

### للويب (اختياري):
1. أنشئ OAuth client ID للويب
2. أضف `http://localhost` إلى Authorized origins

## 4. تحميل google-services.json

1. اذهب إلى "Project Settings" في Firebase Console أو Google Cloud Console
2. أضف تطبيق Android إذا لم يكن موجوداً
3. حمل ملف `google-services.json`
4. ضع الملف في: `android/app/google-services.json`

## 5. تحديث android/app/build.gradle

أضف هذه الأسطر إلى `android/app/build.gradle`:

```gradle
// في أعلى الملف
apply plugin: 'com.google.gms.google-services'

// في dependencies
dependencies {
    implementation 'com.google.gms:google-services:4.3.15'
    // ... باقي dependencies
}
```

## 6. تحديث android/build.gradle

أضف هذا السطر إلى `android/build.gradle`:

```gradle
dependencies {
    classpath 'com.google.gms:google-services:4.3.15'
    // ... باقي dependencies
}
```

## 7. إعداد iOS (اختياري)

### إنشاء iOS OAuth Client:
1. في Google Cloud Console، أنشئ OAuth client ID جديد
2. اختر "iOS"
3. أدخل Bundle ID: `com.example.inventoryManagementApp`

### تحديث ios/Runner/Info.plist:
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>REVERSED_CLIENT_ID</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>YOUR_REVERSED_CLIENT_ID_HERE</string>
        </array>
    </dict>
</array>
```

## 8. اختبار الإعداد

1. شغل التطبيق
2. اذهب إلى الإعدادات > النسخ الاحتياطي والاستعادة
3. انقر على "تسجيل الدخول" في قسم Google Drive
4. يجب أن تظهر شاشة تسجيل الدخول لـ Google

## ملاحظات مهمة:

- **للتطوير**: استخدم debug keystore
- **للنشر**: استخدم release keystore واحصل على SHA-1 الخاص به
- **الأمان**: لا تشارك ملف `google-services.json` في المستودعات العامة
- **الاختبار**: اختبر على جهاز حقيقي للحصول على أفضل النتائج

## استكشاف الأخطاء:

### خطأ "Sign in failed":
- تأكد من صحة Package name
- تأكد من صحة SHA-1 fingerprint
- تأكد من وجود ملف `google-services.json`

### خطأ "API not enabled":
- تأكد من تفعيل Google Drive API
- تأكد من تفعيل Google Sign-In API

### خطأ "Invalid client":
- تأكد من إعداد OAuth client ID بشكل صحيح
- تأكد من مطابقة Package name

## ملفات مطلوبة (يجب إنشاؤها):

1. `android/app/google-services.json` - ملف إعداد Firebase/Google Services
2. تحديث `android/app/build.gradle` - إضافة Google Services plugin
3. تحديث `android/build.gradle` - إضافة Google Services classpath
4. (اختياري) تحديث `ios/Runner/Info.plist` - إعداد iOS

بعد إكمال هذه الخطوات، ستعمل ميزة النسخ الاحتياطي عبر Google Drive بشكل صحيح.
