# دليل استكشاف أخطاء الاختبارات - أسامة ماركت

## 🔍 **دليل شامل لحل مشاكل الاختبارات**

### **🚨 المشاكل الشائعة وحلولها**

## **1. مشكلة: فشل الاختبارات بسبب تداخل البيانات**

**الأعراض:**
```
✗ يجب أن يضيف منتج جديد بشكل صحيح
  Expected: 1
  Actual: 3
```

**السبب:**
- عدم تنظيف قاعدة البيانات بين الاختبارات
- تراكم البيانات من اختبارات سابقة

**الحل:**
```dart
// في بداية ملف الاختبار
setUpAll(() async {
  DatabaseService.enableTestMode();
  await DatabaseService.instance.database;
});

tearDown(() async {
  await DatabaseService.instance.clearDatabaseForTesting();
});

tearDownAll(() async {
  DatabaseService.disableTestMode();
  await DatabaseService.instance.resetDatabase();
});
```

## **2. مشكلة: أخطاء Null Safety**

**الأعراض:**
```
✗ Null check operator used on a null value
✗ The argument type 'Null' can't be assigned to the parameter type 'String'
```

**السبب:**
- استخدام قيم null بدون فحص
- عدم التعامل مع القيم nullable بشكل صحيح

**الحل:**
```dart
// بدلاً من
expect(sale.remainingAmount, equals(50.0));

// استخدم
expect(sale.remainingAmount ?? 0.0, equals(50.0));

// أو
expect(sale.remainingAmount, isNotNull);
expect(sale.remainingAmount!, equals(50.0));
```

## **3. مشكلة: فشل اختبارات التنسيق والترجمة**

**الأعراض:**
```
✗ LocaleDataException: Locale data has not been initialized
✗ Expected: '100.00 ر.س' Actual: '‏100.00 ر.س'
```

**السبب:**
- عدم تهيئة بيانات اللغة
- رموز RTL إضافية في النص

**الحل:**
```dart
// بدلاً من المقارنة المباشرة
expect(Formatters.formatCurrency(100.0), equals('100.00 ر.س'));

// استخدم فحص المحتوى
final String result = Formatters.formatCurrency(100.0);
expect(result.contains('100.00'), isTrue);
expect(result.contains('ر.س'), isTrue);
```

## **4. مشكلة: فشل اختبارات المزودات**

**الأعراض:**
```
✗ يجب أن يحدث قائمة المنتجات بعد الإضافة
  Expected: 1
  Actual: 0
```

**السبب:**
- عدم استدعاء `notifyListeners()` في المزود
- عدم انتظار العمليات غير المتزامنة

**الحل:**
```dart
// في المزود
Future<void> addProduct(Product product) async {
  try {
    final int id = await _productService.addProduct(product);
    product = product.copyWith(id: id);
    _products.add(product);
    notifyListeners(); // مهم جداً!
  } catch (e) {
    // معالجة الخطأ
  }
}

// في الاختبار
test('يجب أن يضيف منتج جديد', () async {
  await productProvider.addProduct(testProduct);
  expect(productProvider.products.length, equals(1));
});
```

## **5. مشكلة: أخطاء في النماذج**

**الأعراض:**
```
✗ The named parameter 'wholesalePrice' isn't defined
✗ Required named parameter 'price' must be provided
```

**السبب:**
- استخدام أسماء خصائص غير موجودة
- عدم تمرير الحقول المطلوبة

**الحل:**
```dart
// تحقق من تعريف النموذج أولاً
final Product product = Product(
  name: 'منتج اختبار',
  price: 10.0,        // مطلوب
  barcode: '123456789',
  category: 'فئة',
  unit: 'قطعة',
  warehouseQuantity: 100,
  storeQuantity: 50,
  retailPrice: 15.0,  // بدلاً من wholesalePrice
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
);
```

## **6. مشكلة: فشل اختبارات الواجهة**

**الأعراض:**
```
✗ Widget test failed: Could not find widget
✗ The following assertion was thrown building MyApp
```

**السبب:**
- عدم تهيئة المزودات في الاختبار
- عدم انتظار تحميل الواجهة

**الحل:**
```dart
testWidgets('اختبار الواجهة', (WidgetTester tester) async {
  // تهيئة المزودات
  await tester.pumpWidget(
    MultiProvider(
      providers: [
        ChangeNotifierProvider<ProductProvider>(
            create: (_) => ProductProvider()),
        ChangeNotifierProvider<CustomerProvider>(
            create: (_) => CustomerProvider()),
      ],
      child: const MaterialApp(home: HomeScreen()),
    ),
  );

  // انتظار تحميل الواجهة
  await tester.pumpAndSettle();

  // التحقق من العناصر
  expect(find.text('أسامة ماركت'), findsOneWidget);
});
```

### **🛠️ أدوات التشخيص**

## **1. تشغيل اختبار واحد:**
```bash
flutter test test/simple_tests.dart -n "اسم الاختبار"
```

## **2. تشغيل مع تفاصيل إضافية:**
```bash
flutter test --verbose
```

## **3. تشغيل مع تقرير التغطية:**
```bash
flutter test --coverage
```

## **4. فحص الكود للأخطاء:**
```bash
flutter analyze
```

## **5. إصلاح الأخطاء التلقائية:**
```bash
dart fix --apply
```

### **📋 قائمة مراجعة الاختبارات**

## **قبل كتابة اختبار جديد:**
- [ ] تحقق من تعريف النماذج والخصائص المطلوبة
- [ ] تأكد من تهيئة قاعدة البيانات للاختبار
- [ ] أضف `setUp()` و `tearDown()` عند الحاجة
- [ ] استخدم `await` مع جميع العمليات غير المتزامنة

## **عند فشل اختبار:**
- [ ] اقرأ رسالة الخطأ بعناية
- [ ] تحقق من القيم المتوقعة والفعلية
- [ ] تأكد من تنظيف البيانات بين الاختبارات
- [ ] فحص النماذج والخصائص المستخدمة

## **بعد إصلاح الأخطاء:**
- [ ] شغل الاختبار المحدد للتأكد من الإصلاح
- [ ] شغل جميع الاختبارات للتأكد من عدم كسر شيء آخر
- [ ] راجع الكود للتأكد من جودته

### **🎯 نصائح للاختبارات الناجحة**

## **1. اكتب اختبارات بسيطة:**
```dart
test('وصف واضح للاختبار', () {
  // ترتيب البيانات
  final testData = createTestData();
  
  // تنفيذ العملية
  final result = performOperation(testData);
  
  // التحقق من النتيجة
  expect(result, equals(expectedValue));
});
```

## **2. استخدم بيانات وهمية:**
```dart
// بدلاً من الاعتماد على بيانات حقيقية
final Product realProduct = await productService.getProduct(1);

// استخدم بيانات وهمية
final Product mockProduct = Product(
  id: 1,
  name: 'منتج اختبار',
  price: 10.0,
);
```

## **3. اختبر حالة واحدة في كل اختبار:**
```dart
// جيد - اختبار واحد لكل حالة
test('يجب أن يضيف منتج جديد', () { ... });
test('يجب أن يحدث منتج موجود', () { ... });
test('يجب أن يحذف منتج', () { ... });

// سيء - اختبار متعدد الحالات
test('يجب أن يدير المنتجات', () {
  // إضافة وتحديث وحذف في اختبار واحد
});
```

## **4. استخدم أسماء وصفية:**
```dart
// جيد
test('يجب أن يرجع خطأ عند إدخال سعر سالب', () { ... });

// سيء
test('اختبار السعر', () { ... });
```

### **🚀 الاختبارات الموصى بها**

## **للبدء السريع:**
```bash
# اختبارات مبسطة وسريعة
flutter test test/simple_tests.dart
```

## **للتطوير:**
```bash
# اختبارات المزودات والخدمات
flutter test test/provider_tests.dart
flutter test test/service_tests.dart
```

## **للنشر:**
```bash
# جميع الاختبارات مع التغطية
flutter test --coverage
```

---

**تذكر:** الاختبارات الجيدة توفر الوقت وتضمن جودة الكود! 🧪✨
