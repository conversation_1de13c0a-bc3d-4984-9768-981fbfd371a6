/// ملف موحد لجميع الـ Enums المشتركة في التطبيق
/// Unified file for all shared enums in the application

/// تردد النسخ الاحتياطي التلقائي
/// Auto backup frequency enum
enum BackupFrequency {
  daily('يومي'),
  weekly('أسبوعي'),
  monthly('شهري');

  const BackupFrequency(this.displayName);
  final String displayName;

  /// الحصول على معلومات التردد
  String get description {
    switch (this) {
      case BackupFrequency.daily:
        return 'نسخة احتياطية كل يوم';
      case BackupFrequency.weekly:
        return 'نسخة احتياطية كل أسبوع';
      case BackupFrequency.monthly:
        return 'نسخة احتياطية كل شهر';
    }
  }

  /// تحويل إلى Duration
  Duration get duration {
    switch (this) {
      case BackupFrequency.daily:
        return const Duration(days: 1);
      case BackupFrequency.weekly:
        return const Duration(days: 7);
      case BackupFrequency.monthly:
        return const Duration(days: 30);
    }
  }
}

/// حالات الفواتير
/// Invoice status enum
enum InvoiceStatus {
  draft('مسودة'),
  pending('معلقة'),
  completed('مكتملة'),
  cancelled('ملغية');

  const InvoiceStatus(this.displayName);
  final String displayName;
}

/// أنواع الدفع
/// Payment method enum
enum PaymentMethod {
  cash('نقداً'),
  creditCard('بطاقة ائتمان'),
  bankTransfer('تحويل بنكي'),
  check('شيك'),
  deferred('آجل');

  const PaymentMethod(this.displayName);
  final String displayName;
}

/// فئات المصروفات
/// Expense category enum
enum ExpenseCategory {
  rent('إيجار', 'rent'),
  salaries('رواتب', 'salaries'),
  supplies('مستلزمات', 'supplies'),
  utilities('خدمات', 'utilities'),
  maintenance('صيانة', 'maintenance'),
  marketing('تسويق', 'marketing'),
  transportation('نقل ومواصلات', 'transportation'),
  miscellaneous('متنوعة', 'miscellaneous');

  const ExpenseCategory(this.displayName, this.value);
  final String displayName;
  final String value;

  /// الحصول على فئة من القيمة
  static ExpenseCategory fromValue(String value) {
    return ExpenseCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => ExpenseCategory.miscellaneous,
    );
  }
}

/// وحدات القياس الافتراضية
/// Default measurement units enum
enum DefaultUnit {
  piece('قطعة', 'pcs'),
  kilogram('كيلو', 'kg'),
  gram('جرام', 'g'),
  liter('لتر', 'l'),
  meter('متر', 'm'),
  box('علبة', 'box'),
  carton('كرتون', 'ctn'),
  item('حبة', 'item');

  const DefaultUnit(this.displayName, this.symbol);
  final String displayName;
  final String symbol;
}

/// حالات الطلبات
/// Order status enum
enum OrderStatus {
  pending('معلقة'),
  completed('مكتملة'),
  cancelled('ملغية'),
  converted('محولة لفاتورة');

  const OrderStatus(this.displayName);
  final String displayName;
}

/// أنواع المعاملات المالية
/// Transaction type enum
enum TransactionType {
  sale('مبيعة'),
  purchase('مشترى'),
  expense('مصروف'),
  payment('دفعة'),
  receipt('إيصال');

  const TransactionType(this.displayName);
  final String displayName;
}

/// حالات المخزون
/// Stock status enum
enum StockStatus {
  inStock('متوفر'),
  lowStock('مخزون منخفض'),
  outOfStock('نفد المخزون'),
  discontinued('متوقف');

  const StockStatus(this.displayName);
  final String displayName;
}

/// أنواع النشاطات
/// Activity type enum
enum ActivityType {
  sale('مبيعة'),
  purchase('مشترى'),
  order('طلبية'),
  expense('مصروف'),
  backup('نسخ احتياطي'),
  login('تسجيل دخول'),
  logout('تسجيل خروج'),
  other('أخرى');

  const ActivityType(this.displayName);
  final String displayName;
}

/// أنواع التقارير
/// Report type enum
enum ReportType {
  sales('تقرير المبيعات'),
  purchases('تقرير المشتريات'),
  inventory('تقرير المخزون'),
  customers('تقرير العملاء'),
  suppliers('تقرير الموردين'),
  expenses('تقرير المصروفات'),
  profit('تقرير الأرباح'),
  daily('التقرير اليومي');

  const ReportType(this.displayName);
  final String displayName;
}

/// أنواع التصدير
/// Export type enum
enum ExportType {
  excel('Excel'),
  pdf('PDF'),
  csv('CSV'),
  json('JSON');

  const ExportType(this.displayName);
  final String displayName;

  /// امتداد الملف
  String get fileExtension {
    switch (this) {
      case ExportType.excel:
        return '.xlsx';
      case ExportType.pdf:
        return '.pdf';
      case ExportType.csv:
        return '.csv';
      case ExportType.json:
        return '.json';
    }
  }
}

/// أنواع الثيمات
/// Theme type enum
enum ThemeType {
  light('فاتح'),
  dark('داكن'),
  system('النظام');

  const ThemeType(this.displayName);
  final String displayName;
}

/// أنواع الإشعارات
/// Notification type enum
enum NotificationType {
  info('معلومات'),
  warning('تحذير'),
  error('خطأ'),
  success('نجح');

  const NotificationType(this.displayName);
  final String displayName;
}
