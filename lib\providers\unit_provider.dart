import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/unit.dart';
import 'package:inventory_management_app/services/unit_service.dart';

/// Provider class for managing unit state and operations
class UnitProvider extends ChangeNotifier {
  List<Unit> _units = <Unit>[];
  final UnitService _unitService = UnitService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of units
  List<Unit> get units => _units;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Fetch all units from the database
  Future<void> fetchUnits() async {
    _setLoading(true);
    _clearError();

    try {
      _units = await _unitService.getAllUnits();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch units: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new unit
  Future<void> addUnit(Unit unit) async {
    _setLoading(true);
    _clearError();

    try {
      await _unitService.insertUnit(unit);
      await fetchUnits();
    } catch (e) {
      _setError('Failed to add unit: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing unit
  Future<void> updateUnit(Unit unit) async {
    _setLoading(true);
    _clearError();

    try {
      await _unitService.updateUnit(unit);
      await fetchUnits();
    } catch (e) {
      _setError('Failed to update unit: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a unit
  Future<void> deleteUnit(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _unitService.deleteUnit(id);
      await fetchUnits();
    } catch (e) {
      _setError('Failed to delete unit: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Search units by name
  Future<void> searchUnits(String name) async {
    _setLoading(true);
    _clearError();

    try {
      _units = await _unitService.searchUnitsByName(name);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search units: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Check if unit name already exists
  Future<bool> unitNameExists(String name, {int? excludeId}) async {
    try {
      return await _unitService.unitNameExists(name, excludeId: excludeId);
    } catch (e) {
      _setError('Failed to check unit name: $e');
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
