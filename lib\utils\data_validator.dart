import 'dart:convert';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/supplier.dart';
import '../models/sale.dart';
import '../models/purchase.dart';

/// فئة للتحقق من صحة البيانات
class DataValidator {
  /// التحقق من صحة بيانات المنتج
  static ValidationResult validateProduct(Product product) {
    final List<String> errors = <String>[];

    // التحقق من الاسم
    if (product.name.trim().isEmpty) {
      errors.add('اسم المنتج مطلوب');
    } else if (product.name.length < 2) {
      errors.add('اسم المنتج يجب أن يكون أكثر من حرفين');
    } else if (product.name.length > 100) {
      errors.add('اسم المنتج يجب أن يكون أقل من 100 حرف');
    }

    // التحقق من الفئة
    if (product.category == null || product.category!.trim().isEmpty) {
      errors.add('فئة المنتج مطلوبة');
    }

    // التحقق من الوحدة
    if (product.unit == null || product.unit!.trim().isEmpty) {
      errors.add('وحدة المنتج مطلوبة');
    }

    // التحقق من الكمية
    if (product.quantity == null || product.quantity! < 0) {
      errors.add('كمية المنتج يجب أن تكون صفر أو أكثر');
    }

    // التحقق من سعر الشراء
    if (product.purchasePrice == null || product.purchasePrice! < 0) {
      errors.add('سعر الشراء يجب أن يكون صفر أو أكثر');
    }

    // التحقق من سعر البيع
    if (product.salePrice == null || product.salePrice! < 0) {
      errors.add('سعر البيع يجب أن يكون صفر أو أكثر');
    }

    // التحقق من أن سعر البيع أكبر من سعر الشراء
    if (product.purchasePrice != null && product.salePrice != null) {
      if (product.salePrice! < product.purchasePrice!) {
        errors.add('سعر البيع يجب أن يكون أكبر من أو يساوي سعر الشراء');
      }
    }

    // التحقق من الحد الأدنى
    if (product.minLevel == null || product.minLevel! < 0) {
      errors.add('الحد الأدنى للمخزون يجب أن يكون صفر أو أكثر');
    }

    // التحقق من الباركود إذا كان موجوداً
    if (product.barcode != null && product.barcode!.isNotEmpty) {
      if (!_isValidBarcode(product.barcode!)) {
        errors.add('الباركود غير صحيح');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة بيانات العميل
  static ValidationResult validateCustomer(Customer customer) {
    final List<String> errors = <String>[];

    // التحقق من الاسم
    if (customer.name.trim().isEmpty) {
      errors.add('اسم العميل مطلوب');
    } else if (customer.name.length < 2) {
      errors.add('اسم العميل يجب أن يكون أكثر من حرفين');
    } else if (customer.name.length > 100) {
      errors.add('اسم العميل يجب أن يكون أقل من 100 حرف');
    }

    // التحقق من رقم الهاتف
    if (customer.phone == null || customer.phone!.trim().isEmpty) {
      errors.add('رقم الهاتف مطلوب');
    } else if (!_isValidPhoneNumber(customer.phone!)) {
      errors.add('رقم الهاتف غير صحيح');
    }

    // التحقق من البريد الإلكتروني إذا كان موجوداً
    if (customer.email != null && customer.email!.isNotEmpty) {
      if (!_isValidEmail(customer.email!)) {
        errors.add('البريد الإلكتروني غير صحيح');
      }
    }

    // التحقق من الرصيد
    if (customer.balance != null &&
        (customer.balance!.isNaN || customer.balance!.isInfinite)) {
      errors.add('الرصيد غير صحيح');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة بيانات المورد
  static ValidationResult validateSupplier(Supplier supplier) {
    final List<String> errors = <String>[];

    // التحقق من الاسم
    if (supplier.name == null || supplier.name!.trim().isEmpty) {
      errors.add('اسم المورد مطلوب');
    } else if (supplier.name!.length < 2) {
      errors.add('اسم المورد يجب أن يكون أكثر من حرفين');
    } else if (supplier.name!.length > 100) {
      errors.add('اسم المورد يجب أن يكون أقل من 100 حرف');
    }

    // التحقق من رقم الهاتف
    if (supplier.phone == null || supplier.phone!.trim().isEmpty) {
      errors.add('رقم الهاتف مطلوب');
    } else if (!_isValidPhoneNumber(supplier.phone!)) {
      errors.add('رقم الهاتف غير صحيح');
    }

    // التحقق من البريد الإلكتروني إذا كان موجوداً
    if (supplier.email != null && supplier.email!.isNotEmpty) {
      if (!_isValidEmail(supplier.email!)) {
        errors.add('البريد الإلكتروني غير صحيح');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة بيانات البيع
  static ValidationResult validateSale(Sale sale) {
    final List<String> errors = <String>[];

    // التحقق من التاريخ
    if (sale.date == null) {
      errors.add('تاريخ البيع مطلوب');
    } else {
      try {
        final DateTime saleDate = DateTime.parse(sale.date!);
        if (saleDate.isAfter(DateTime.now())) {
          errors.add('تاريخ البيع لا يمكن أن يكون في المستقبل');
        }
      } catch (e) {
        errors.add('تاريخ البيع غير صحيح');
      }
    }

    // التحقق من المبلغ الإجمالي
    if (sale.total == null || sale.total! < 0) {
      errors.add('المبلغ الإجمالي يجب أن يكون صفر أو أكثر');
    }

    // التحقق من طريقة الدفع
    if (sale.paymentMethod == null || sale.paymentMethod!.trim().isEmpty) {
      errors.add('طريقة الدفع مطلوبة');
    }

    // التحقق من الحالة
    if (sale.status == null || sale.status!.trim().isEmpty) {
      errors.add('حالة البيع مطلوبة');
    } else if (!_isValidSaleStatus(sale.status!)) {
      errors.add('حالة البيع غير صحيحة');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة بيانات الشراء
  static ValidationResult validatePurchase(Purchase purchase) {
    final List<String> errors = <String>[];

    // التحقق من التاريخ
    if (purchase.date == null) {
      errors.add('تاريخ الشراء مطلوب');
    } else {
      try {
        final DateTime purchaseDate = DateTime.parse(purchase.date!);
        if (purchaseDate.isAfter(DateTime.now())) {
          errors.add('تاريخ الشراء لا يمكن أن يكون في المستقبل');
        }
      } catch (e) {
        errors.add('تاريخ الشراء غير صحيح');
      }
    }

    // التحقق من المبلغ الإجمالي
    if (purchase.total == null || purchase.total! < 0) {
      errors.add('المبلغ الإجمالي يجب أن يكون صفر أو أكثر');
    }

    // التحقق من المورد
    if (purchase.supplierId == null) {
      errors.add('المورد مطلوب');
    }

    // التحقق من الحالة
    if (purchase.status == null || purchase.status!.trim().isEmpty) {
      errors.add('حالة الشراء مطلوبة');
    } else if (!_isValidPurchaseStatus(purchase.status!)) {
      errors.add('حالة الشراء غير صحيحة');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة JSON
  static ValidationResult validateJson(String jsonString) {
    final List<String> errors = <String>[];

    try {
      json.decode(jsonString);
    } catch (e) {
      errors.add('تنسيق JSON غير صحيح: $e');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة التاريخ
  static ValidationResult validateDate(DateTime? date,
      {bool allowFuture = false}) {
    final List<String> errors = <String>[];

    if (date == null) {
      errors.add('التاريخ مطلوب');
    } else {
      if (!allowFuture && date.isAfter(DateTime.now())) {
        errors.add('التاريخ لا يمكن أن يكون في المستقبل');
      }

      // التحقق من أن التاريخ ليس قديماً جداً (أكثر من 100 سنة)
      final DateTime hundredYearsAgo =
          DateTime.now().subtract(const Duration(days: 365 * 100));
      if (date.isBefore(hundredYearsAgo)) {
        errors.add('التاريخ قديم جداً');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة المبلغ
  static ValidationResult validateAmount(double? amount,
      {double? min, double? max}) {
    final List<String> errors = <String>[];

    if (amount == null) {
      errors.add('المبلغ مطلوب');
    } else {
      if (amount.isNaN || amount.isInfinite) {
        errors.add('المبلغ غير صحيح');
      }

      if (min != null && amount < min) {
        errors.add('المبلغ يجب أن يكون $min أو أكثر');
      }

      if (max != null && amount > max) {
        errors.add('المبلغ يجب أن يكون $max أو أقل');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة الكمية
  static ValidationResult validateQuantity(int? quantity,
      {int? min, int? max}) {
    final List<String> errors = <String>[];

    if (quantity == null) {
      errors.add('الكمية مطلوبة');
    } else {
      if (min != null && quantity < min) {
        errors.add('الكمية يجب أن تكون $min أو أكثر');
      }

      if (max != null && quantity > max) {
        errors.add('الكمية يجب أن تكون $max أو أقل');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  // دوال مساعدة خاصة
  static bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  static bool _isValidPhoneNumber(String phone) {
    // إزالة المسافات والرموز
    final String cleanPhone = phone.replaceAll(RegExp(r'[^\d+]'), '');

    // التحقق من الأرقام السعودية
    return RegExp(r'^(\+966|966|0)?[5][0-9]{8}$').hasMatch(cleanPhone) ||
        RegExp(r'^(\+966|966|0)?[1][0-9]{7}$').hasMatch(cleanPhone);
  }

  static bool _isValidBarcode(String barcode) {
    // التحقق من أن الباركود يحتوي على أرقام فقط وطوله مناسب
    return RegExp(r'^\d{8,13}$').hasMatch(barcode);
  }

  static bool _isValidSaleStatus(String status) {
    const List<String> validStatuses = <String>[
      'draft',
      'pending',
      'completed',
      'cancelled'
    ];
    return validStatuses.contains(status.toLowerCase());
  }

  static bool _isValidPurchaseStatus(String status) {
    const List<String> validStatuses = <String>[
      'draft',
      'pending',
      'completed',
      'cancelled'
    ];
    return validStatuses.contains(status.toLowerCase());
  }
}

/// نتيجة التحقق من صحة البيانات
class ValidationResult {
  final bool isValid;
  final List<String> errors;

  ValidationResult({
    required this.isValid,
    required this.errors,
  });

  /// الحصول على أول خطأ
  String? get firstError => errors.isNotEmpty ? errors.first : null;

  /// الحصول على جميع الأخطاء كنص واحد
  String get errorsAsString => errors.join('\n');

  /// التحقق من وجود خطأ معين
  bool hasError(String error) => errors.contains(error);

  /// إضافة خطأ جديد
  void addError(String error) {
    errors.add(error);
  }

  /// إضافة أخطاء متعددة
  void addErrors(List<String> newErrors) {
    errors.addAll(newErrors);
  }

  /// دمج نتيجة تحقق أخرى
  void merge(ValidationResult other) {
    errors.addAll(other.errors);
  }

  @override
  String toString() {
    return 'ValidationResult(isValid: $isValid, errors: $errors)';
  }
}
