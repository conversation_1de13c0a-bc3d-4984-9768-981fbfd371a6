/// Model class representing a customer account statement entry
class CustomerStatement {
  /// Unique identifier for the statement entry
  int? id;
  
  /// Customer ID this statement belongs to
  int? customerId;
  
  /// Date of the transaction
  String? date;
  
  /// Description of the transaction
  String? description;
  
  /// Amount of the transaction
  double? amount;
  
  /// Running balance after this transaction
  double? balance;

  /// Constructor for creating a CustomerStatement instance
  CustomerStatement({
    this.id,
    this.customerId,
    this.date,
    this.description,
    this.amount,
    this.balance,
  });

  /// Converts the CustomerStatement instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'customerId': customerId,
      'date': date,
      'description': description,
      'amount': amount,
      'balance': balance,
    };
  }

  /// Creates a CustomerStatement instance from a Map (typically from database)
  factory CustomerStatement.fromMap(Map<String, dynamic> map) {
    return CustomerStatement(
      id: map['id'] as int?,
      customerId: map['customerId'] as int?,
      date: map['date'] as String?,
      description: map['description'] as String?,
      amount: map['amount']?.toDouble(),
      balance: map['balance']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'CustomerStatement{id: $id, customerId: $customerId, date: $date, '
        'description: $description, amount: $amount, balance: $balance}';
  }
}
