@echo off
chcp 65001 >nul
echo 🚀 تشغيل تطبيق أسامة ماركت
echo ================================

echo 📁 التحقق من المسار الحالي...
echo المسار الحالي: %CD%
echo.

echo 🔍 التحقق من وجود Flutter...
where flutter >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter غير موجود في PATH
    echo يرجى تثبيت Flutter أو إضافته لمتغير PATH
    pause
    exit /b 1
)

echo ✅ Flutter موجود
flutter --version
echo.

echo 🧹 تنظيف المشروع...
flutter clean
if %errorlevel% neq 0 (
    echo ❌ فشل في تنظيف المشروع
    pause
    exit /b 1
)

echo.
echo 📦 جلب التبعيات...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ فشل في جلب التبعيات
    echo تحقق من ملف pubspec.yaml
    pause
    exit /b 1
)

echo.
echo 📱 فحص الأجهزة المتاحة...
flutter devices

echo.
echo 🚀 محاولة تشغيل التطبيق...
flutter run
if %errorlevel% neq 0 (
    echo ❌ فشل في تشغيل التطبيق
    echo جرب: flutter run -d chrome للتشغيل على الويب
)

echo.
echo ================================
echo 🏁 انتهى التشغيل
pause
