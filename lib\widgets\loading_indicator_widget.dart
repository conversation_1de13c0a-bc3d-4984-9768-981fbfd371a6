import 'package:flutter/material.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import '../config/app_colors.dart';
import '../config/app_styles.dart';

/// 🔄 مؤشر التحميل المخصص لأسامة ماركت
class LoadingIndicatorWidget extends StatelessWidget {
  final String? message;
  final double? size;
  final Color? color;
  final bool showMessage;
  final bool showAnimatedText;

  const LoadingIndicatorWidget({
    super.key,
    this.message,
    this.size = 40.0,
    this.color,
    this.showMessage = true,
    this.showAnimatedText = true,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // مؤشر التحميل الدائري
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              strokeWidth: 3.0,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? AppColors.primary,
              ),
            ),
          ),

          if (showMessage) ...<Widget>[
            const SizedBox(height: 16),

            // النص المتحرك أو الثابت
            if (showAnimatedText) _buildAnimatedText() else _buildStaticText(),
          ],
        ],
      ),
    );
  }

  Widget _buildAnimatedText() {
    return AnimatedTextKit(
      animatedTexts: <AnimatedText>[
        TyperAnimatedText(
          message ?? 'جاري التحميل...',
          textStyle: AppStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
          speed: const Duration(milliseconds: 100),
        ),
      ],
      totalRepeatCount: 1,
      displayFullTextOnTap: true,
    );
  }

  Widget _buildStaticText() {
    return Text(
      message ?? 'جاري التحميل...',
      style: AppStyles.bodyMedium.copyWith(
        color: AppColors.textSecondary,
        fontWeight: FontWeight.w500,
      ),
      textAlign: TextAlign.center,
    );
  }
}

/// مؤشر تحميل صغير للاستخدام داخل الأزرار
class SmallLoadingIndicator extends StatelessWidget {
  final Color? color;
  final double size;

  const SmallLoadingIndicator({
    super.key,
    this.color,
    this.size = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2.0,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? AppColors.textOnPrimary,
        ),
      ),
    );
  }
}

/// مؤشر تحميل للصفحات الكاملة
class FullPageLoadingIndicator extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final Color? backgroundColor;

  const FullPageLoadingIndicator({
    super.key,
    this.title,
    this.subtitle,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor ?? AppColors.background,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradient,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            // شعار أو أيقونة
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.store_outlined,
                size: 40,
                color: AppColors.textOnPrimary,
              ),
            ),

            const SizedBox(height: 32),

            // العنوان
            if (title != null)
              Text(
                title!,
                style: AppStyles.titleLarge.copyWith(
                  color: AppColors.primary,
                ),
                textAlign: TextAlign.center,
              ),

            const SizedBox(height: 16),

            // مؤشر التحميل
            const LoadingIndicatorWidget(
              size: 50.0,
              showAnimatedText: true,
            ),

            const SizedBox(height: 16),

            // النص الفرعي
            if (subtitle != null)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  subtitle!,
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// مؤشر تحميل للبطاقات
class CardLoadingIndicator extends StatelessWidget {
  final double height;
  final String? message;

  const CardLoadingIndicator({
    super.key,
    this.height = 120.0,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppColors.cardShadow,
      ),
      child: LoadingIndicatorWidget(
        message: message ?? 'جاري تحميل البيانات...',
        size: 30.0,
        showAnimatedText: false,
      ),
    );
  }
}

/// مؤشر تحميل مع تقدم
class ProgressLoadingIndicator extends StatelessWidget {
  final double progress;
  final String? message;
  final Color? progressColor;

  const ProgressLoadingIndicator({
    super.key,
    required this.progress,
    this.message,
    this.progressColor,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // مؤشر التقدم الدائري
          SizedBox(
            width: 60,
            height: 60,
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                CircularProgressIndicator(
                  value: progress,
                  strokeWidth: 4.0,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    progressColor ?? AppColors.primary,
                  ),
                  backgroundColor: AppColors.divider,
                ),
                Text(
                  '${(progress * 100).toInt()}%',
                  style: AppStyles.labelMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

          if (message != null) ...<Widget>[
            const SizedBox(height: 16),
            Text(
              message!,
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// مؤشر تحميل للقوائم
class ListLoadingIndicator extends StatelessWidget {
  final int itemCount;
  final double itemHeight;

  const ListLoadingIndicator({
    super.key,
    this.itemCount = 5,
    this.itemHeight = 80.0,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: (BuildContext context, int index) {
        return Container(
          height: itemHeight,
          margin: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: AppColors.cardShadow,
          ),
          child: const Center(
            child: SmallLoadingIndicator(),
          ),
        );
      },
    );
  }
}
