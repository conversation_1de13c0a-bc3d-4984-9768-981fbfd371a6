import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_colors.dart';

import '../../widgets/modern_drawer.dart';
import '../../widgets/quick_actions_grid.dart';
import '../../widgets/conditional_bottom_nav.dart';
import '../../utils/navigation_manager.dart';
import '../../providers/product_provider.dart';
import '../../providers/sale_provider.dart';
import '../../providers/purchase_provider.dart';
import '../../providers/customer_provider.dart';
import '../../providers/supplier_provider.dart';

/// الشاشة الرئيسية للتطبيق
class DashboardScreen extends StatefulWidget {
  /// Constructor for DashboardScreen
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;

      try {
        // تحميل البيانات بشكل متدرج لتجنب الحمل الثقيل
        await context.read<ProductProvider>().fetchProducts();
        if (!mounted) return;

        await Future.delayed(const Duration(milliseconds: 100));
        if (!mounted) return;

        await context.read<CustomerProvider>().fetchCustomers();
        if (!mounted) return;

        await Future.delayed(const Duration(milliseconds: 100));
        if (!mounted) return;

        await context.read<SaleProvider>().fetchSales();
        if (!mounted) return;

        await Future.delayed(const Duration(milliseconds: 100));
        if (!mounted) return;

        await context.read<PurchaseProvider>().fetchPurchases();
        if (!mounted) return;

        await Future.delayed(const Duration(milliseconds: 100));
        if (!mounted) return;

        await context.read<SupplierProvider>().fetchSuppliers();
      } catch (e) {
        debugPrint('خطأ في تحميل البيانات: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler(
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: MainScreenWrapper(
          title: 'أسامة ماركت',
          needsDrawer: true,
          child: Scaffold(
            drawer: const ModernDrawer(),
            body: RefreshIndicator(
              onRefresh: () async {
                if (!mounted) return;
                try {
                  await context.read<ProductProvider>().fetchProducts();
                  if (!mounted) return;
                  await context.read<CustomerProvider>().fetchCustomers();
                } catch (e) {
                  debugPrint('خطأ في تحديث البيانات: $e');
                }
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    // ترحيب
                    _buildWelcomeSection(),
                    const SizedBox(height: 24),

                    // الإجراءات السريعة
                    _buildQuickActionsSection(),
                    const SizedBox(height: 24),

                    // التنبيهات والإشعارات
                    _buildAlertsSection(),
                    const SizedBox(height: 24),

                    // النشاط الأخير
                    _buildRecentActivitySection(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Text(
                  'مرحباً بك',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textOnPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'إدارة محل المواد الغذائية',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.textOnPrimary.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'اليوم: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textOnPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.store,
              color: AppColors.textOnPrimary,
              size: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return const QuickActionsGrid();
  }

  Widget _buildAlertsSection() {
    return Consumer<ProductProvider>(
      builder: (BuildContext context, ProductProvider productProvider,
          Widget? child) {
        // حساب المنتجات منخفضة المخزون
        final List<Product> lowStockProducts = productProvider.products
            .where((Product product) => (product.quantity ?? 0) < 10)
            .toList();

        if (lowStockProducts.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              'التنبيهات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              color: AppColors.warning.withOpacity(0.1),
              child: InkWell(
                onTap: () => context.go('/products'),
                borderRadius: BorderRadius.circular(8),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: <Widget>[
                      Icon(
                        Icons.warning,
                        color: AppColors.warning,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            const Text(
                              'تنبيه مخزون منخفض',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${lowStockProducts.length} منتج يحتاج إعادة تخزين',
                              style: TextStyle(
                                color: AppColors.textSecondary,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: AppColors.textSecondary,
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecentActivitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            const Text(
              'النشاط الأخير',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () => context.go('/activities'),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: <Widget>[
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.add_box,
                      color: AppColors.primary,
                      size: 20,
                    ),
                  ),
                  title: const Text('تم إضافة منتج جديد'),
                  subtitle: const Text('منذ ساعتين'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                ),
                const Divider(height: 16, thickness: 0.5),
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.accent.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.point_of_sale,
                      color: AppColors.accent,
                      size: 20,
                    ),
                  ),
                  title: const Text('عملية بيع جديدة'),
                  subtitle: const Text('منذ 3 ساعات'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
