class Expense {
  final int? id;
  final DateTime? expenseDate;
  final String? category;
  final double? amount;
  final String? description;
  final String? notes;
  final String? status;

  Expense({
    this.id,
    this.expenseDate,
    this.category,
    this.amount,
    this.description,
    this.notes,
    this.status,
  });

  /// Convert Expense to Map for database storage
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'expenseDate': expenseDate?.toIso8601String(),
      'category': category,
      'amount': amount,
      'description': description,
      'notes': notes,
      'status': status ?? 'active',
    };
  }

  /// Create Expense from Map (database result)
  factory Expense.fromMap(Map<String, dynamic> map) {
    return Expense(
      id: map['id'],
      expenseDate: map['expenseDate'] != null
          ? DateTime.parse(map['expenseDate'])
          : null,
      category: map['category'],
      amount: map['amount']?.toDouble(),
      description: map['description'],
      notes: map['notes'],
      status: map['status'],
    );
  }

  /// Create a copy of this Expense with some fields replaced
  Expense copyWith({
    int? id,
    DateTime? expenseDate,
    String? category,
    double? amount,
    String? description,
    String? notes,
    String? status,
  }) {
    return Expense(
      id: id ?? this.id,
      expenseDate: expenseDate ?? this.expenseDate,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  /// Get formatted amount with currency
  String get formattedAmount {
    return '${amount?.toStringAsFixed(2) ?? '0.00'} ر.س';
  }

  /// Get formatted date
  String get formattedDate {
    if (expenseDate == null) return 'غير محدد';
    return '${expenseDate!.day}/${expenseDate!.month}/${expenseDate!.year}';
  }

  /// Get category display name in Arabic
  String get categoryDisplayName {
    switch (category) {
      case 'rent':
        return 'إيجار';
      case 'salaries':
        return 'رواتب';
      case 'supplies':
        return 'مستلزمات';
      case 'utilities':
        return 'خدمات';
      case 'maintenance':
        return 'صيانة';
      case 'marketing':
        return 'تسويق';
      case 'transportation':
        return 'نقل ومواصلات';
      case 'miscellaneous':
        return 'متنوعة';
      default:
        return category ?? 'غير محدد';
    }
  }

  /// Check if expense is active
  bool get isActive {
    return status == 'active';
  }

  /// Get available expense categories
  static List<Map<String, String>> get availableCategories {
    return <Map<String, String>>[
      <String, String>{'value': 'rent', 'label': 'إيجار'},
      <String, String>{'value': 'salaries', 'label': 'رواتب'},
      <String, String>{'value': 'supplies', 'label': 'مستلزمات'},
      <String, String>{'value': 'utilities', 'label': 'خدمات'},
      <String, String>{'value': 'maintenance', 'label': 'صيانة'},
      <String, String>{'value': 'marketing', 'label': 'تسويق'},
      <String, String>{'value': 'transportation', 'label': 'نقل ومواصلات'},
      <String, String>{'value': 'miscellaneous', 'label': 'متنوعة'},
    ];
  }

  @override
  String toString() {
    return 'Expense{id: $id, expenseDate: $expenseDate, category: $category, amount: $amount, description: $description, notes: $notes, status: $status}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Expense &&
        other.id == id &&
        other.expenseDate == expenseDate &&
        other.category == category &&
        other.amount == amount &&
        other.description == description &&
        other.notes == notes &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        expenseDate.hashCode ^
        category.hashCode ^
        amount.hashCode ^
        description.hashCode ^
        notes.hashCode ^
        status.hashCode;
  }
}
